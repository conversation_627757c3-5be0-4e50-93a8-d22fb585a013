<?php
require_once 'utils.php';
require_once('./apple/generate_client_secret.php');
require_once 'vendor/autoload.php';
require_once 'mailsender.php';
require_once('models/ResultModel.php');
require_once 'login_tracking.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
// Apply CORS headers
cors_client();
$is_debug = true;
//For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT'];
if (preg_match('/python/i', $user_agent)) {
    echo 'You are forbidden!';
    foreach ($_POST as $key => $value) {
        echo "a";
        // Include StripeLogger if not already included
        if (!class_exists('StripeLogger')) {
            require_once 'stripe/StripeLogger.php';
        }
        StripeLogger::log(StripeLogLevel::WARNING, "SECURITY - Python script request detected", [
            'key' => $key,
            'value' => $value
        ]);
    }
    header('HTTP/1.0 403 Forbidden');
    return;
}
/* ============================================================================================== */
/*                                        language options                                        */
/* ============================================================================================== */
// Import language configuration
require_once 'language_config.php';
$selectedLanguage = getSelectedLanguage();
/* ============================================================================================== */
/*                                        language strings                                        */
/* ============================================================================================== */
// Import language strings from external file
require_once 'language_strings.php';
//$uri = $_SERVER['REQUEST_URI'];   // /api/users
//$method = $_SERVER['REQUEST_METHOD'];  // GET,POST,DELETE, etc.
include_once('config.php');
$payload = file_get_contents('php://input');
$data = json_decode($payload, true);
if ($data == null) {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Payload is not a valid json or null";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
$functName = "";
if (isset($data['f'])) {
    $functName = $data['f'];
} else {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint variable is not set";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}
$validEndpoints = [
    "login_user",
    "logout_user",
    "register_user",
    "get_google_tokens",
    "refresh",
    "get_twitter_tokens",
    "get_apple_tokens",
    "forgot_password",
    "reset_password",
    "verify_email",
    "resend_verification_email",
    "check_token_validity",
    "check_password_reset_token",
    "change_password"
];
if (in_array($functName, $validEndpoints)) {
    if ($functName == "login_user") {
        $remember = isset($data['remember']) ? $data['remember'] : false;
        $functName($data['email'], $data['password'], $remember);
    } else if ($functName == "logout_user") {
        $functName();
    } else if ($functName == "register_user") {
        $username = isset($data['username']) ? $data['username'] : null;
        $full_name = isset($data['full_name']) ? $data['full_name'] : null;
        $country = isset($data['country']) ? $data['country'] : null;
        $terms_accepted = isset($data['terms_accepted']) ? $data['terms_accepted'] : false;
        $captcha_token = isset($data['captcha_token']) ? $data['captcha_token'] : null;
        $functName($data['email'], $data['password'], $username, $full_name, $country, $terms_accepted, $captcha_token);
    } else if ($functName == "get_google_tokens") {
        $functName($data['code']);
    } else if ($functName == "refresh") {
        $functName();
    } else if ($functName == "get_twitter_tokens") {
        $functName();
    } else if ($functName == "get_apple_tokens") {
        $functName($data['code'], $data['name']);
    } else if ($functName == "forgot_password") {
        $functName($data['email']);
    } else if ($functName == "reset_password") {
        $functName($data['token'], $data['new_password']);
    } else if ($functName == "check_password_reset_token") {
        $token = $data['token'];
        check_password_reset_token($token);
    }
    else if ($functName == "change_password") {
        $userId = authenticate_user();
        if ($userId) {
            change_password($userId, $data['current_password'], $data['new_password']);
        } else {
            $response = new ErrorResult('Unauthorized access');
            $response->send(401);
        }
    }

    else if ($functName == "verify_email") {
        require_once 'email_verification.php';
        $result = verify_email($data['token']);
        if ($result['success']) {
            $response = new SuccessResult($result['message']);
            $response->send();
        } else {
            $response = new ErrorResult($result['message']);
            $response->send(400);
        }
    } else if ($functName == "resend_verification_email") {
        require_once 'email_verification.php';
        // Check if email is provided (for non-authenticated users)
        if (isset($data['email'])) {
            resend_verification_email_by_email($data['email']);
        } else {
            // Fallback to authenticated user method
            $userId = authenticate_user();
            if ($userId) {
                resend_verification_email($userId);
            } else {
                global $selectedLanguage, $errorMessages;
                $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                $response = new ErrorResult($errorMessages[$lang]['email_address_required']);
                $response->send(400);
            }
        }
    } else if ($functName == "check_token_validity") {
        require_once 'email_verification.php';
        require_once 'stripe/StripeLogger.php';
        $token = $data['token'];
        // Token'ın geçerliliğini kontrol et ama e-postayı doğrulama
        global $link;

        StripeLogger::log(StripeLogLevel::DEBUG, "TOKEN VALIDITY CHECK - Starting validation", [
            'token_preview' => substr($token, 0, 10) . "..."
        ]);

        if (empty($token)) {
            StripeLogger::log(StripeLogLevel::WARNING, "TOKEN VALIDITY CHECK - Empty token provided");
            global $selectedLanguage, $errorMessages;
            $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new ErrorResult($errorMessages[$lang]['invalid_verification_token']);
            $response->send(400);
            return;
        }
        // Check if the token exists in the users table
        $checkTokenQuery = "SELECT id, email_verified, email_verification_sent_at FROM users WHERE email_verification_token = ?";
        $checkTokenStmt = mysqli_prepare($link, $checkTokenQuery);
        if (!$checkTokenStmt) {
            StripeLogger::log(StripeLogLevel::ERROR, "TOKEN VALIDITY CHECK - Database prepare error", [
                'error' => mysqli_error($link)
            ]);
            $response = new ErrorResult('Database error');
            $response->send(500);
            return;
        }
        mysqli_stmt_bind_param($checkTokenStmt, "s", $token);
        mysqli_stmt_execute($checkTokenStmt);
        $checkTokenResult = mysqli_stmt_get_result($checkTokenStmt);
        $tokenUser = mysqli_fetch_assoc($checkTokenResult);

        StripeLogger::log(StripeLogLevel::DEBUG, "TOKEN VALIDITY CHECK - Database query result", [
            'token_preview' => substr($token, 0, 10) . "...",
            'user_found' => $tokenUser ? 'Yes' : 'No',
            'user_id' => $tokenUser ? $tokenUser['id'] : 'N/A',
            'email_verified' => $tokenUser ? $tokenUser['email_verified'] : 'N/A',
            'sent_at' => $tokenUser ? $tokenUser['email_verification_sent_at'] : 'N/A'
        ]);

        // If token doesn't exist in the database
        if (!$tokenUser) {
            StripeLogger::log(StripeLogLevel::INFO, "TOKEN VALIDITY CHECK - Token not found in database", [
                'token_preview' => substr($token, 0, 10) . "..."
            ]);
            global $selectedLanguage, $errorMessages;
            $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new ErrorResult($errorMessages[$lang]['invalid_verification_token']);
            $response->send(400);
            return;
        }
        // If the account is already verified
        if ($tokenUser['email_verified'] == 1) {
            StripeLogger::log(StripeLogLevel::INFO, "TOKEN VALIDITY CHECK - Email already verified", [
                'user_id' => $tokenUser['id']
            ]);
            global $selectedLanguage, $errorMessages;
            $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new SuccessResult($errorMessages[$lang]['email_already_verified']);
            $response->send();
            return;
        }
        // Now check if the token is still valid (not expired)
        $query = "SELECT id FROM users
                  WHERE email_verification_token = ?
                  AND email_verified = 0
                  AND email_verification_sent_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            StripeLogger::log(StripeLogLevel::ERROR, "TOKEN VALIDITY CHECK - Database prepare error for expiry check", [
                'error' => mysqli_error($link)
            ]);
            $response = new ErrorResult('Database error');
            $response->send(500);
            return;
        }
        mysqli_stmt_bind_param($stmt, "s", $token);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        if (!$user = mysqli_fetch_assoc($result)) {
            StripeLogger::log(StripeLogLevel::INFO, "TOKEN VALIDITY CHECK - Token expired", [
                'token_preview' => substr($token, 0, 10) . "...",
                'user_id' => $tokenUser['id'],
                'sent_at' => $tokenUser['email_verification_sent_at']
            ]);
            global $selectedLanguage, $errorMessages;
            $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new ErrorResult($errorMessages[$lang]['verification_token_expired']);
            $response->send(400);
            return;
        }
        StripeLogger::log(StripeLogLevel::INFO, "TOKEN VALIDITY CHECK - Token is valid", [
            'user_id' => $user['id']
        ]);
        $response = new SuccessResult('Token is valid');
        $response->send();
    }
} else {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint Not Exists";
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
}
/* ============================================================================================== */
/*                                            endpoints                                           */
/* ============================================================================================== */
function get_apple_tokens($code, $name)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    StripeLogger::log(StripeLogLevel::INFO, "APPLE AUTH STARTED - Processing Apple authentication request", [
        'code_length' => strlen($code),
        'code_preview' => substr($code, 0, 20) . "...",
        'name_param' => $name
    ]);

    $id_token = $code;
    $jwt_payload = explode(".", $id_token)[1];
    $jwt_payload_decoded = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $jwt_payload)), true);

    StripeLogger::log(StripeLogLevel::DEBUG, "APPLE AUTH - JWT payload decoded", [
        'payload' => $jwt_payload_decoded
    ]);

    $sub = $jwt_payload_decoded['sub'] ?? null;
    $email = $jwt_payload_decoded['email'] ?? null;

    if (!$sub) {
        StripeLogger::log(StripeLogLevel::ERROR, "APPLE AUTH ERROR - User ID (sub) is missing from token payload");
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['invalid_auth_token']);
        $response->send(400);
        return;
    }

    if (!$email) {
        StripeLogger::log(StripeLogLevel::WARNING, "APPLE AUTH WARNING - Email is missing from token payload");
    }

    StripeLogger::log(StripeLogLevel::INFO, "APPLE AUTH - User identified", [
        'sub' => $sub,
        'email' => $email ? substr($email, 0, 2) . '***' . strstr($email, '@') : 'null',
        'name' => $name
    ]);
    // return [
    //     'sub' => $sub,
    //     'email' => $email,
    //     'name' => $name,
    //     'raw' => $jwt_payload_decoded
    // ];
    // Kullanıcı adı belirleme
    StripeLogger::log(StripeLogLevel::DEBUG, "APPLE AUTH - Processing name parameter", [
        'name' => $name
    ]);

    // Set a default display name using the email
    $displayName = $email;

    // Try to extract name if available
    if ($name !== null) {
        $firstName = null;
        $lastName = null;

        // Check different possible formats
        if (is_array($name) && isset($name['name']) && is_array($name['name'])) {
            $firstName = $name['name']['firstName'] ?? null;
            $lastName = $name['name']['lastName'] ?? null;
        } elseif (is_array($name) && isset($name['firstName']) && isset($name['lastName'])) {
            $firstName = $name['firstName'] ?? null;
            $lastName = $name['lastName'] ?? null;
        }

        // Use name if we found one
        if ($firstName || $lastName) {
            $displayName = trim("$firstName $lastName");
        }
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "APPLE AUTH - Using display name: " . $displayName);
    $appleUser = getOrCreateOauthUser($sub, $email, $displayName, RegistrationMethod::APPLE);

    // Check if user was created/retrieved successfully
    if (!$appleUser) {
        StripeLogger::log(StripeLogLevel::ERROR, "APPLE AUTH ERROR - Failed to create or retrieve user", [
            'sub' => $sub
        ]);
        $response = new ErrorResult('Failed to create user account');
        $response->send(500);
        return;
    }

    // Log successful authentication
    StripeLogger::log(StripeLogLevel::INFO, "APPLE AUTH SUCCESS - User authenticated successfully", [
        'user_id' => $appleUser['id'],
        'email' => $appleUser['email'] ? substr($appleUser['email'], 0, 2) . '***' . strstr($appleUser['email'], '@') : 'null',
        'email_verified' => $appleUser['email_verified'] ? 'Yes' : 'No'
    ]);

    // OAuth login'lerde varsayılan olarak 30 gün remember me kullan
    $expires_at = time() + (60 * 60 * 24 * 30); // 30 gün
    $jwt = createAuthToken($appleUser['id']);
    $refresh_token = createRefreshToken($expires_at);
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $userId = $appleUser['id'];
    if (!storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)) {
        $response = new ErrorResult('Server Error');
        $response->send(500);
        return;
    }

    // Track successful login
    handleSuccessfulLogin($userId, 'apple', ['attempted_email' => $appleUser['email'] ?? null]);

    $response = new SuccessResult(['token' => $jwt, 'user' => $appleUser['provider_username']]);
    $response->send();
    return;
    // // error_log("Received authorization code: " . $code);
    // $client_id = "com.zdc.eviotlogin";
    // $client_secret = generateAppleClientSecret();
    // $redirect_uri = "https://api.coinscout.app/apple/callback.php";
    // $post_data = [
    //     'client_id' => $client_id,
    //     'client_secret' => $client_secret,
    //     'code' => $code,
    //     'grant_type' => 'authorization_code',
    //     'redirect_uri' => $redirect_uri
    // ];
    // $ch = curl_init();
    // curl_setopt($ch, CURLOPT_URL, "https://appleid.apple.com/auth/token");
    // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    // curl_setopt($ch, CURLOPT_POST, true);
    // curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    // curl_setopt($ch, CURLOPT_HTTPHEADER, [
    //     'Content-Type: application/x-www-form-urlencoded',
    //     'Accept: application/json'
    // ]);
    // $response = curl_exec($ch);
    // if (curl_errno($ch)) {
    //     error_log("CURL Error: " . curl_error($ch));
    // }
    // $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    // error_log("HTTP Response Code: " . $http_code);
    // error_log("Apple API Response: " . $response);
    // curl_close($ch);
    // $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    // curl_close($ch);
    // $response_data = json_decode($response, true);
    // if (isset($response_data['id_token'])) {
    //     $id_token = $response_data['id_token'];
    //     $jwt_payload = explode(".", $id_token)[1];
    //     $jwt_payload_decoded = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $jwt_payload)), true);
    //     $sub = $jwt_payload_decoded['sub'] ?? null;
    //     $email = $jwt_payload_decoded['email'] ?? null;
    //     $name = $jwt_payload_decoded['name'] ?? null;
    //     error_log("Apple User ID (sub): " . $sub);
    //     error_log("Apple Email: " . $email);
    //     error_log("Apple Name: " . json_encode($name));
    //     return [
    //         'sub' => $sub,
    //         'email' => $email,
    //         'name' => $name,
    //         'raw' => $jwt_payload_decoded
    //     ];
    // }
    // error_log("Received authorization success ");
    // return null;
}
function log_jwt_contents($jwt_token)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    try {
        $jwt_parts = explode(".", $jwt_token);
        // Base64 decode the payload (middle part)
        $payload = base64_decode(str_replace(
            ['-', '_'],
            ['+', '/'],
            $jwt_parts[1]
        ));

        StripeLogger::log(StripeLogLevel::DEBUG, "JWT - Payload decoded", [
            'raw_payload' => $payload
        ]);

        // Decode JSON
        $decoded = json_decode($payload, true);

        // Log each field separately
        $fields = [];
        foreach ($decoded as $key => $value) {
            $fields[$key] = $value;
        }

        StripeLogger::log(StripeLogLevel::DEBUG, "JWT - Fields extracted", [
            'fields' => $fields
        ]);

        return $decoded;
    } catch (\Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "JWT ERROR - Error decoding JWT: " . $e->getMessage(), [
            'exception' => get_class($e)
        ]);
        return null;
    }
}
function get_google_tokens($code = null)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // İlk olarak direkt parametre olarak gelen code'u kontrol et
    $client_token = $code;
    // Eğer code parametresi boşsa, gelen JSON verisini kontrol et
    if (!$client_token) {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        $client_token = isset($data['code']) ? $data['code'] : null;
        // f parametresi ile gelen istekleri de kontrol et
        if (!$client_token && isset($data['f']) && $data['f'] === 'get_google_tokens') {
            $client_token = isset($data['code']) ? $data['code'] : null;
        }
    }
    if (!$client_token) {
        StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - Authorization code missing");
        $response = new ErrorResult('Authorization code missing');
        $response->send(400);
        return;
    }
    StripeLogger::log(StripeLogLevel::INFO, "GOOGLE AUTH STARTED - Processing Google authentication request", [
        'token_preview' => substr($client_token, 0, 30) . "..."
    ]);

    // Google API client kütüphanesi yüklü olmayabilir, bu yüzden try-catch bloğu içinde çalıştıralım
    try {
        // Google API'yi kullanmak için gerekli sınıfları yükleyelim
        require_once 'vendor/autoload.php';

        // Google_Client sınıfını kullanmayı deneyelim
        if (class_exists('Google_Client')) {
            $client = new Google_Client();
        } elseif (class_exists('\Google_Client')) {
            $client = new \Google_Client();
        } else {
            throw new \Exception("Google_Client class not found");
        }
    } catch (\Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - API client initialization failed", [
            'error' => $e->getMessage(),
            'exception' => get_class($e)
        ]);
        $response = new ErrorResult('Server configuration error. Please contact support.');
        $response->send(500);
        return;
    }
    $client->setClientId('231351638588-9m45dcrtfp5sslqisi22n39k520q1lv4.apps.googleusercontent.com');
    $client->setClientSecret('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');
    $client->setRedirectUri('https://coinscout.app');
    try {
        $token = $client->fetchAccessTokenWithAuthCode($client_token);
        StripeLogger::log(StripeLogLevel::DEBUG, "GOOGLE AUTH - API response received", [
            'token' => $token
        ]);

        if (isset($token['id_token'])) {
            $id_token = $token['id_token'];
            $payload = $client->verifyIdToken($id_token);
            if ($payload) {
                $googleId = $payload['sub'];
                $email = $payload['email'];
                $givenName = isset($payload['given_name']) ? $payload['given_name'] : null;
                $familyName = isset($payload['family_name']) ? $payload['family_name'] : null;
                $userInfo = null;

                // Kullanıcı adını oluştur
                if ($givenName && $familyName) {
                    $userInfo = $givenName . ' ' . $familyName;
                } elseif ($givenName) {
                    $userInfo = $givenName;
                } elseif ($familyName) {
                    $userInfo = $familyName;
                } else {
                    $userInfo = $email;
                }

                StripeLogger::log(StripeLogLevel::INFO, "GOOGLE AUTH - User identified", [
                    'google_id' => $googleId,
                    'email' => substr($email, 0, 2) . '***' . strstr($email, '@'),
                    'user_info' => $userInfo
                ]);

                // Kullanıcıyı veritabanında oluştur veya güncelle
                $googleUser = getOrCreateOauthUser($googleId, $email, $userInfo, RegistrationMethod::GOOGLE);

                // Check if user was created/retrieved successfully
                if (!$googleUser) {
                    StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - Failed to create or retrieve user", [
                        'google_id' => $googleId
                    ]);
                    $response = new ErrorResult('Failed to create user account');
                    $response->send(500);
                    return;
                }

                // Log successful authentication
                StripeLogger::log(StripeLogLevel::INFO, "GOOGLE AUTH SUCCESS - User authenticated successfully", [
                    'user_id' => $googleUser['id'],
                    'email' => substr($googleUser['email'], 0, 2) . '***' . strstr($googleUser['email'], '@'),
                    'email_verified' => $googleUser['email_verified'] ? 'Yes' : 'No'
                ]);

                // Token'ları oluştur - OAuth login'lerde varsayılan olarak 30 gün remember me kullan
                $expires_at = time() + (60 * 60 * 24 * 30); // 30 gün
                $jwt = createAuthToken($googleUser['id']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];
                $userId = $googleUser['id'];

                // Refresh token'ı kaydet
                if (!storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)) {
                    StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - Failed to store refresh token", [
                        'user_id' => $userId
                    ]);
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }

                StripeLogger::log(StripeLogLevel::INFO, "GOOGLE AUTH COMPLETE - Tokens created and stored successfully", [
                    'user_id' => $userId,
                    'expires_at' => date('Y-m-d H:i:s', $expires_at)
                ]);

                // Track successful login
                handleSuccessfulLogin($userId, 'google', ['attempted_email' => $email]);

                // Başarılı yanıt döndür
                $response = new SuccessResult([
                    'token' => $jwt,
                    'user' => $userInfo,
                    'email' => $email
                ]);
                $response->send();
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - ID token verification failed");

                global $selectedLanguage, $errorMessages;
                $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                $response = new ErrorResult($errorMessages[$lang]['token_dogrulama_basarisiz']);
                $response->send(400);
            }
        } else {
            StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - No ID token in response", [
                'token_response' => $token
            ]);
            $response = new ErrorResult('ID Token alınamadı. Lütfen tekrar deneyin.');
            $response->send(400);
        }
    } catch (\Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GOOGLE AUTH ERROR - Exception: " . $e->getMessage(), [
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString()
        ]);
        $response = new ErrorResult('Google Token Error: ' . $e->getMessage());
        $response->send(400);
    }
}
function get_twitter_tokens()
{
    // StripeLogger sınıfını dahil et
    require_once 'stripe/StripeLogger.php';

    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    $client_token = isset($data['code']) ? $data['code'] : null;

    StripeLogger::log(StripeLogLevel::INFO, "TWITTER AUTH STARTED - Processing Twitter authentication request");

    if (!$client_token) {
        StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Authorization code missing");
        $response = new ErrorResult('Authorization code missing');
        $response->send(400);
        return;
    }

    $twitter_client_id = 'M2RkWTBONHVvYTlMMTRkTzBLZlg6MTpjaQ';
    $twitter_client_secret = 'M9xq-XjNEkm8v4IsZezAHc1Ji8kS5PhzhWjc1aFeLeAmaq7xa4';
    $redirect_uri = 'https://coinscout.app/callback';

    // Code verifier'ı frontend'den almak gerekecek
    $code_verifier = isset($data['code_verifier']) ? $data['code_verifier'] : null;
    if (!$code_verifier) {
        StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Code verifier missing");
        $response = new ErrorResult('Code verifier missing');
        $response->send(400);
        return;
    }

    try {
        StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - Requesting access token from Twitter API", [
            'client_token_length' => strlen($client_token),
            'code_verifier_length' => strlen($code_verifier),
            'redirect_uri' => $redirect_uri
        ]);

        $token_url = 'https://api.twitter.com/2/oauth2/token';
        $token_data = [
            'code' => $client_token,
            'grant_type' => 'authorization_code',
            'client_id' => $twitter_client_id,
            'client_secret' => $twitter_client_secret,
            'redirect_uri' => $redirect_uri,
            'code_verifier' => $code_verifier,
            // 'scope' => 'tweet.read users.read' // E-posta izni kaldırıldı
        ];

        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30 saniye zaman aşımı
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // SSL sertifikasını doğrula
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Yönlendirmeleri takip et
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($twitter_client_id . ':' . $twitter_client_secret),
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);

        // İstek zamanını kaydet
        $request_time = microtime(true);

        // İsteği gönder
        $token_response = curl_exec($ch);

        // Yanıt zamanını hesapla
        $response_time = microtime(true) - $request_time;

        // CURL bilgilerini al
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        $curl_errno = curl_errno($ch);

        // İstek detaylarını logla
        StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - Token request details", [
            'http_code' => $http_code,
            'response_time' => round($response_time * 1000) . 'ms',
            'curl_errno' => $curl_errno
        ]);

        if ($curl_error) {
            StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - CURL error when requesting token", [
                'error' => $curl_error,
                'errno' => $curl_errno,
                'http_code' => $http_code
            ]);
            throw new \Exception("Twitter API connection error: " . $curl_error);
        }

        // HTTP yanıt kodunu kontrol et
        if ($http_code >= 400) {
            StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - HTTP error from Twitter API", [
                'http_code' => $http_code,
                'response' => $token_response
            ]);

            // Yanıtı JSON olarak ayrıştırmaya çalış
            $error_data = json_decode($token_response, true);
            if ($error_data && isset($error_data['error_description'])) {
                throw new \Exception("Twitter API error: " . $error_data['error_description']);
            } else {
                throw new \Exception("Twitter API HTTP error: " . $http_code);
            }
        }

        // Tam yanıtı loglayalım (hata ayıklama için)
        StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - Raw token response", [
            'http_code' => $http_code,
            'response' => $token_response
        ]);

        $token_data = json_decode($token_response, true);

        if (!$token_data) {
            StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Invalid JSON response from Twitter API", [
                'http_code' => $http_code,
                'response' => $token_response
            ]);
            throw new \Exception("Invalid response from Twitter API");
        }

        if (isset($token_data['error'])) {
            StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Twitter API returned error", [
                'error' => $token_data['error'],
                'error_description' => $token_data['error_description'] ?? 'No description'
            ]);
            throw new \Exception("Twitter API error: " . ($token_data['error_description'] ?? $token_data['error']));
        }

        if (isset($token_data['access_token'])) {
            StripeLogger::log(StripeLogLevel::INFO, "TWITTER AUTH - Successfully obtained access token");

            // Kullanıcı bilgilerini al (e-posta olmadan)
            $user_url = 'https://api.twitter.com/2/users/me?user.fields=id,name,username';
            $user_ch = curl_init($user_url);

            // CURL seçeneklerini ayarla
            curl_setopt($user_ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $token_data['access_token'],
                'Content-Type: application/json',
                'Accept: application/json'
            ]);
            curl_setopt($user_ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($user_ch, CURLOPT_TIMEOUT, 30); // 30 saniye zaman aşımı
            curl_setopt($user_ch, CURLOPT_SSL_VERIFYPEER, true); // SSL sertifikasını doğrula
            curl_setopt($user_ch, CURLOPT_FOLLOWLOCATION, true); // Yönlendirmeleri takip et

            // İstek zamanını kaydet
            $request_time = microtime(true);

            // İsteği gönder
            $user_response = curl_exec($user_ch);

            // Yanıt zamanını hesapla
            $response_time = microtime(true) - $request_time;

            // CURL bilgilerini al
            $user_http_code = curl_getinfo($user_ch, CURLINFO_HTTP_CODE);
            $user_curl_error = curl_error($user_ch);
            $user_curl_errno = curl_errno($user_ch);

            // İstek detaylarını logla
            StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - API request details", [
                'url' => $user_url,
                'http_code' => $user_http_code,
                'response_time' => round($response_time * 1000) . 'ms',
                'curl_errno' => $user_curl_errno
            ]);

            if ($user_curl_error) {
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - CURL error when requesting user data", [
                    'error' => $user_curl_error,
                    'errno' => $user_curl_errno,
                    'http_code' => $user_http_code
                ]);
                throw new \Exception("Twitter API connection error: " . $user_curl_error);
            }

            // HTTP yanıt kodunu kontrol et
            if ($user_http_code >= 400) {
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - HTTP error from Twitter API", [
                    'http_code' => $user_http_code,
                    'response' => $user_response
                ]);
                throw new \Exception("Twitter API HTTP error: " . $user_http_code);
            }

            $user_data = json_decode($user_response, true);

            // Tam yanıtı loglayalım (hata ayıklama için)
            StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - Raw API response", [
                'http_code' => $user_http_code,
                'response' => $user_response
            ]);

            if (!$user_data) {
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Invalid JSON response for user data", [
                    'http_code' => $user_http_code,
                    'response' => $user_response
                ]);
                throw new \Exception("Invalid user data response from Twitter API");
            }

            // Yanıtı loglayalım (hassas bilgileri maskeleyerek)
            $log_user_data = $user_data;
            if (isset($log_user_data['data']['email'])) {
                $email_parts = explode('@', $log_user_data['data']['email']);
                if (count($email_parts) == 2) {
                    $masked_username = substr($email_parts[0], 0, 2) . str_repeat('*', max(strlen($email_parts[0]) - 4, 1)) . substr($email_parts[0], -2);
                    $log_user_data['data']['email'] = $masked_username . '@' . $email_parts[1];
                }
            }

            StripeLogger::log(StripeLogLevel::DEBUG, "TWITTER AUTH - User data structure", [
                'user_data_keys' => is_array($user_data) ? array_keys($user_data) : 'not_array',
                'data_keys' => isset($user_data['data']) && is_array($user_data['data']) ? array_keys($user_data['data']) : 'data_not_found_or_not_array',
                'errors' => isset($user_data['errors']) ? $user_data['errors'] : null
            ]);

            // Twitter API hata kontrolü
            if (isset($user_data['errors']) && !empty($user_data['errors'])) {
                $errorMessages = [];
                foreach ($user_data['errors'] as $error) {
                    $errorMessages[] = isset($error['message']) ? $error['message'] : 'Unknown error';
                    StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - API returned error", [
                        'error_code' => $error['code'] ?? 'unknown',
                        'error_message' => $error['message'] ?? 'unknown',
                        'error_type' => $error['type'] ?? 'unknown'
                    ]);
                }

                $errorMsg = implode(', ', $errorMessages);
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - API errors: " . $errorMsg);

                // Kullanıcıya daha genel bir hata mesajı göster
                $response = new ErrorResult('Twitter kullanıcı bilgileri alınamadı. Lütfen daha sonra tekrar deneyin.');
                $response->send(400);
                return;
            }

            // Veri yapısı kontrolü
            if (!isset($user_data['data']) || !is_array($user_data['data'])) {
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Invalid data structure", [
                    'user_data' => $user_data
                ]);
                $response = new ErrorResult('Twitter kullanıcı bilgileri alınamadı. Geçersiz veri yapısı.');
                $response->send(400);
                return;
            }

            if (isset($user_data['data']['id'])) {
                $twitterId = $user_data['data']['id'];

                // Kullanıcı adı ve isim kontrolü
                if (!isset($user_data['data']['username']) || !isset($user_data['data']['name'])) {
                    StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Missing username or name", [
                        'twitter_id' => $twitterId,
                        'has_username' => isset($user_data['data']['username']),
                        'has_name' => isset($user_data['data']['name'])
                    ]);
                    $response = new ErrorResult('Twitter kullanıcı bilgileri eksik. Lütfen daha sonra tekrar deneyin.');
                    $response->send(400);
                    return;
                }

                $username = $user_data['data']['username'];
                $name = $user_data['data']['name'];
                $email = isset($user_data['data']['email']) ? $user_data['data']['email'] : null;

                StripeLogger::log(StripeLogLevel::INFO, "TWITTER AUTH - User identified", [
                    'twitter_id' => $twitterId,
                    'username' => $username,
                    'has_email' => !empty($email)
                ]);

                // E-posta yoksa kullanıcıya bilgi ver
                if (!$email) {
                    StripeLogger::log(StripeLogLevel::WARNING, "TWITTER AUTH - User has no email, creating placeholder", [
                        'twitter_id' => $twitterId
                    ]);
                    // Geçici e-posta oluştur (kullanıcı daha sonra güncelleyebilir)
                    $email = "twitter_" . $twitterId . "@placeholder.coinscout.app";
                }

                // Kullanıcıyı veritabanında kontrol et veya oluştur
                $twitterUser = getOrCreateOauthUser($twitterId, $email, $username, RegistrationMethod::TWITTER);

                // Check if user was created/retrieved successfully
                if (!$twitterUser) {
                    StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Failed to create or retrieve user", [
                        'twitter_id' => $twitterId
                    ]);
                    $response = new ErrorResult('Failed to create user account');
                    $response->send(500);
                    return;
                }

                // Log successful authentication
                StripeLogger::log(StripeLogLevel::INFO, "TWITTER AUTH SUCCESS - User authenticated successfully", [
                    'user_id' => $twitterUser['id'],
                    'username' => $twitterUser['provider_username'],
                    'email_verified' => $twitterUser['email_verified'] ? 'Yes' : 'No'
                ]);

                $expires_at = time() + (60 * 60 * 24 * 60); // 30 gün
                $jwt = createAuthToken($twitterUser['id']);
                $refresh_token = createRefreshToken($expires_at);
                $user_ip = $_SERVER['REMOTE_ADDR'];
                $userId = $twitterUser['id'];

                if (!storeRefreshToken($userId, $refresh_token, $expires_at, $user_ip)) {
                    StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Failed to store refresh token", [
                        'user_id' => $userId
                    ]);
                    $response = new ErrorResult('Server Error');
                    $response->send(500);
                    return;
                }

                StripeLogger::log(StripeLogLevel::INFO, "TWITTER AUTH COMPLETE - Tokens created and stored successfully", [
                    'user_id' => $userId,
                    'expires_at' => date('Y-m-d H:i:s', $expires_at)
                ]);

                // Track successful login
                handleSuccessfulLogin($userId, 'twitter', ['attempted_email' => null]);

                $response = new SuccessResult([
                    'token' => $jwt,
                    'user' => $name
                ]);
                $response->send();
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - User ID not found in response", [
                    'response_keys' => array_keys($user_data),
                    'data_keys' => isset($user_data['data']) ? array_keys($user_data['data']) : 'data_not_found'
                ]);
                $response = new ErrorResult('Twitter kullanıcı bilgileri alınamadı.');
                $response->send(400);
            }
        } else {
            StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Access token not found in response", [
                'response_keys' => array_keys($token_data)
            ]);
            $response = new ErrorResult('Access Token alınamadı.');
            $response->send(400);
        }
    } catch (\Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "TWITTER AUTH ERROR - Exception: " . $e->getMessage(), [
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString()
        ]);
        $response = new ErrorResult('Twitter Token Error: ' . $e->getMessage());
        $response->send(400);
    }
}
// function getTwitterToken()
// {
//     $code_verifier = '';
//     $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
//     for ($i = 0; $i < 64; $i++) {
//         $code_verifier .= $chars[random_int(0, strlen($chars) - 1)];
//     }
//     $code_challenge = rtrim(strtr(base64_encode(hash('sha256', $code_verifier, true)), '+/', '-_'), '=');
//     $twitter_client_id = 'M2RkWTBONHVvYTlMMTRkTzBLZlg6MTpjaQ';
//     $twitter_client_secret = 'M9xq-XjNEkm8v4IsZezAHc1Ji8kS5PhzhWjc1aFeLeAmaq7xa4';
//     $redirect_uri = 'https://coinscout.app';
//     function generateRandomState($length = 16) {
//         return bin2hex(random_bytes($length));
//     }
//     $authorization_url = 'https://twitter.com/i/oauth2/authorize?' . http_build_query([
//         'response_type' => 'code',
//         'client_id' => $twitter_client_id,
//         'redirect_uri' => $redirect_uri,
//         'scope' => 'tweet.read users.read', // İzinleri ayarlayın
//         'state' => generateRandomState(), // CSRF koruması
//         'code_challenge' => $code_challenge,
//         'code_challenge_method' => 'S256'
//     ]);
//     $token_url = 'https://api.twitter.com/2/oauth2/token';
//     $token_data = [
//         'code' => $code,
//         'grant_type' => 'authorization_code',
//         'client_id' => $twitter_client_id,
//         'client_secret' => $twitter_client_secret,
//         'redirect_uri' => $redirect_uri,
//         'code_verifier' => $code_verifier
//     ];
//     $ch = curl_init($token_url);
//     curl_setopt($ch, CURLOPT_POST, true);
//     curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//     curl_setopt($ch, CURLOPT_HTTPHEADER, [
//         'Content-Type: application/x-www-form-urlencoded',
//         'Accept: application/json'
//     ]);
//     $token_response = curl_exec($ch);
//     $token_data = json_decode($token_response, true);
//     // Kullanıcı bilgilerini alma
//     if (isset($token_data['access_token'])) {
//         $user_url = 'https://api.twitter.com/2/users/me';
//         $user_ch = curl_init($user_url);
//         curl_setopt($user_ch, CURLOPT_HTTPHEADER, [
//             'Authorization: Bearer ' . $token_data['access_token']
//         ]);
//         curl_setopt($user_ch, CURLOPT_RETURNTRANSFER, true);
//         $user_response = curl_exec($user_ch);
//         $user_data = json_decode($user_response, true);
//         // Kullanıcı işlemleri
//         // Örneğin: veritabanına kaydetme, JWT token oluşturma vs.
//         // Frontend'e yönlendirme
//         exit;
//     }
//     // clientId M2RkWTBONHVvYTlMMTRkTzBLZlg6MTpjaQ
//     // Secret M9xq-XjNEkm8v4IsZezAHc1Ji8kS5PhzhWjc1aFeLeAmaq7xa4
// }
function getOrCreateOauthUser($providerId, $providerEmail = null, $providerUsername = null, $registeredVia)
{
    global $link;

    // StripeLogger sınıfını dahil et (eğer dahil edilmediyse)
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "OAUTH USER - Looking up user with provider ID: $providerId", [
        'provider_id' => $providerId,
        'provider_email' => $providerEmail ? substr($providerEmail, 0, 2) . '***' . strstr($providerEmail, '@') : 'null',
        'provider_username' => $providerUsername,
        'registered_via' => $registeredVia
    ]);

    $query = "SELECT * FROM users WHERE provider_user_id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "s", $providerId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if ($user) {
        StripeLogger::log(StripeLogLevel::INFO, "OAUTH USER - Existing user found", [
            'user_id' => $user['id'],
            'provider_id' => $providerId,
            'email' => $user['email'] ? substr($user['email'], 0, 2) . '***' . strstr($user['email'], '@') : 'null'
        ]);
        return $user;
    } else {
        StripeLogger::log(StripeLogLevel::INFO, "OAUTH USER - Creating new user for provider ID: $providerId");
        mysqli_begin_transaction($link);
        try {
            // Yeni kullanıcıyı oluştur - email_verified=1 olarak ayarla
            $insertQuery = "INSERT INTO users (provider_user_id, provider_username, registered_via, email, email_verified)
                            VALUES (?, ?, ?, ?, 1)";
            $insertStmt = mysqli_prepare($link, $insertQuery);
            mysqli_stmt_bind_param($insertStmt, "ssss", $providerId, $providerUsername, $registeredVia, $providerEmail);

            if (!mysqli_stmt_execute($insertStmt)) {
                $error = mysqli_error($link);
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to insert new user", [
                    'provider_id' => $providerId,
                    'error' => $error
                ]);
                throw new \Exception('User registration failed: ' . $error);
            }

            // Retrieve the user ID immediately after successful execution
            $userId = mysqli_insert_id($link);
            if (!$userId) {
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to retrieve user ID after insert");
                throw new \Exception('Failed to retrieve the user ID after registration.');
            }

            StripeLogger::log(StripeLogLevel::INFO, "OAUTH USER - New user created", [
                'user_id' => $userId,
                'provider_id' => $providerId
            ]);

            // Get localized content based on selected language
            global $defaultContent, $selectedLanguage;
            $lang = isset($defaultContent[$selectedLanguage]) ? $selectedLanguage : 'en';

            // My Watchlist oluştur
            $watchlistName = $defaultContent[$lang]['watchlist_name'];
            $watchlistDescription = $defaultContent[$lang]['watchlist_description'];
            $insertWatchlistQuery = "INSERT INTO watchlists (user_id, name, description) VALUES (?, ?, ?)";
            $insertWatchlistStmt = mysqli_prepare($link, $insertWatchlistQuery);
            mysqli_stmt_bind_param($insertWatchlistStmt, "iss", $userId, $watchlistName, $watchlistDescription);

            if (!mysqli_stmt_execute($insertWatchlistStmt)) {
                $error = mysqli_error($link);
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to create watchlist", [
                    'user_id' => $userId,
                    'error' => $error
                ]);
                throw new \Exception('Watchlist creation failed: ' . $error);
            }

            StripeLogger::log(StripeLogLevel::DEBUG, "OAUTH USER - Watchlist created for user", [
                'user_id' => $userId
            ]);

            // My Portfolio oluştur
            $portfolioName = $defaultContent[$lang]['portfolio_name'];
            $portfolioDescription = $defaultContent[$lang]['portfolio_description'];
            $insertPortfolioQuery = "INSERT INTO portfolio (user_id, name, description) VALUES (?, ?, ?)";
            $insertPortfolioStmt = mysqli_prepare($link, $insertPortfolioQuery);
            mysqli_stmt_bind_param($insertPortfolioStmt, "iss", $userId, $portfolioName, $portfolioDescription);

            if (!mysqli_stmt_execute($insertPortfolioStmt)) {
                $error = mysqli_error($link);
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to create portfolio", [
                    'user_id' => $userId,
                    'error' => $error
                ]);
                throw new \Exception('Portfolio creation failed: ' . $error);
            }

            StripeLogger::log(StripeLogLevel::DEBUG, "OAUTH USER - Portfolio created for user", [
                'user_id' => $userId
            ]);

            mysqli_commit($link);
            StripeLogger::log(StripeLogLevel::INFO, "OAUTH USER - Transaction committed successfully");

            // Yeni kullanıcıyı sorgula
            $newUserQuery = "SELECT * FROM users WHERE provider_user_id = ?";
            $newUserStmt = mysqli_prepare($link, $newUserQuery);

            if (!$newUserStmt) {
                $error = mysqli_error($link);
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to prepare statement to query new user", [
                    'error' => $error
                ]);
                throw new \Exception('Failed to query user data: ' . $error);
            }

            mysqli_stmt_bind_param($newUserStmt, "s", $providerId);

            if (!mysqli_stmt_execute($newUserStmt)) {
                $error = mysqli_error($link);
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Failed to execute query for new user", [
                    'error' => $error
                ]);
                throw new \Exception('Failed to query user data: ' . $error);
            }

            $newResult = mysqli_stmt_get_result($newUserStmt);
            $newUser = mysqli_fetch_assoc($newResult);

            if (!$newUser) {
                StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - User was created but could not be retrieved", [
                    'provider_id' => $providerId
                ]);
                throw new \Exception('User was created but could not be retrieved');
            }

            StripeLogger::log(StripeLogLevel::INFO, "OAUTH USER - New user retrieved successfully", [
                'user_id' => $newUser['id'],
                'provider_id' => $providerId
            ]);

            return $newUser;
        } catch (\Exception $e) {
            mysqli_rollback($link);
            StripeLogger::log(StripeLogLevel::ERROR, "OAUTH USER ERROR - Exception during user creation: " . $e->getMessage(), [
                'exception' => get_class($e),
                'provider_id' => $providerId
            ]);
            $response = new ErrorResult($e->getMessage());
            $response->send(500);
            return;
        }
    }
}
function refresh()
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;
    $headers = apache_request_headers();

    // Refresh token cookie'den alınır (Authorization header opsiyonel)
    if (!isset($_COOKIE['refresh_token'])) {
        StripeLogger::log(StripeLogLevel::INFO, "TOKEN REFRESH FAILED - Missing refresh token cookie");
        $response = new ErrorResult('Refresh token not found. Please login again.');
        $response->send(401);
        return;
    }

    $refresh_token = $_COOKIE['refresh_token'];

    StripeLogger::log(StripeLogLevel::DEBUG, "TOKEN REFRESH - Processing refresh token request", [
        'token_preview' => substr($refresh_token, 0, 10) . "...",
        'user_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);

    // Refresh token'ı veritabanından kontrol et
    $query = "SELECT token, user_id, expires FROM refresh_tokens WHERE token = ? AND revoked = 0";
    $stmt = mysqli_prepare($link, $query);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s",  $refresh_token);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $refreshToken = mysqli_fetch_assoc($result);

        if ($refreshToken && strtotime($refreshToken['expires']) > time()) {
            StripeLogger::log(StripeLogLevel::INFO, "TOKEN REFRESH SUCCESS - Valid refresh token", [
                'user_id' => $refreshToken['user_id']
            ]);

            // Yeni JWT token oluştur
            $jwt = createAuthToken($refreshToken["user_id"]);

            StripeLogger::log(StripeLogLevel::DEBUG, "TOKEN REFRESH COMPLETE - New JWT created", [
                'user_id' => $refreshToken['user_id']
            ]);

            // Track successful login via refresh token
            handleSuccessfulLogin($refreshToken['user_id'], 'refresh_token', ['attempted_email' => null]);

            $response = new SuccessResult(['token' => $jwt]);
            $response->send();
        } else {
            StripeLogger::log(StripeLogLevel::INFO, "TOKEN REFRESH FAILED - Invalid or expired refresh token", [
                'token_preview' => substr($refresh_token, 0, 10) . "...",
                'token_exists' => $refreshToken ? 'Yes' : 'No',
                'token_expired' => $refreshToken ? (strtotime($refreshToken['expires']) <= time() ? 'Yes' : 'No') : 'N/A'
            ]);

            $response = new ErrorResult('Refresh token is invalid or expired. Please login again.');
            $response->send(401);
        }
    } else {
        StripeLogger::log(StripeLogLevel::ERROR, "TOKEN REFRESH ERROR - Database error", [
            'error' => mysqli_error($link)
        ]);

        $response = new ErrorResult('Database error occurred during token refresh.');
        $response->send(500);
    }
}
/**
 * Login User with Remember Me functionality
 *
 * KULLANIM ÖRNEĞİ:
 *
 * JavaScript/Frontend:
 * ```javascript
 * const login = async (email, password, rememberMe = false) => {
 *     try {
 *         const response = await fetch('https://api.coinscout.app/authentication.php', {
 *             method: 'POST',
 *             headers: {
 *                 'Content-Type': 'application/json'
 *             },
 *             credentials: 'include', // ÖNEMLİ: Cookie'leri dahil etmek için
 *             body: JSON.stringify({
 *                 f: 'login_user',
 *                 email: email,
 *                 password: password,
 *                 remember: rememberMe // true = 30 gün, false/undefined = session
 *             })
 *         });
 *
 *         const data = await response.json();
 *
 *         if (data.success) {
 *             const token = data.output.token;
 *             localStorage.setItem('auth_token', token);
 *             console.log('Login başarılı');
 *             return token;
 *         } else {
 *             console.error('Login hatası:', data.errormsg);
 *             return null;
 *         }
 *     } catch (error) {
 *         console.error('Login request hatası:', error);
 *         return null;
 *     }
 * };
 *
 * // Kullanım örnekleri:
 * login('<EMAIL>', 'password123', true);  // 30 gün hatırla
 * login('<EMAIL>', 'password123', false); // Session-based
 * login('<EMAIL>', 'password123');        // Session-based (default)
 * ```
 *
 * REFRESH TOKEN SÜRELERİ:
 * - remember = true:  30 gün (kalıcı cookie)
 * - remember = false: Session-based (tarayıcı kapanınca siler)
 *
 * @param string $email User email
 * @param string $password User password
 * @param bool $remember Remember me option (default: false)
 */
function login_user($email, $password, $remember = false)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    if (empty($email) || empty($password)) {
        StripeLogger::log(StripeLogLevel::WARNING, "LOGIN FAILED - Empty email or password");

        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['email_password_empty']);
        $response->send(400);
        return;
    }

    global $link;

    StripeLogger::log(StripeLogLevel::DEBUG, "LOGIN ATTEMPT - Processing login request", [
        'email' => substr($email, 0, 2) . '***' . strstr($email, '@'),
        'remember_me' => $remember ? 'Yes' : 'No'
    ]);

    $query = "SELECT id, password_hash, email_verified FROM users WHERE email = ?";
    $stmt = mysqli_prepare($link, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            // Check if user has no password (created via Stripe webhook)
            if (empty($user['password_hash'])) {
                StripeLogger::log(StripeLogLevel::INFO, "LOGIN ATTEMPT - User has no password (Stripe user), sending password reset", [
                    'user_id' => $user['id'],
                    'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
                ]);

                global $selectedLanguage, $errorMessages;
                $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                // Use output buffering to capture and discard forgot_password response
                ob_start();
                forgot_password($email);
                ob_end_clean(); // Discard the output from forgot_password

                $response = new ErrorResult($errorMessages[$lang]['stripe_user_password_reset_required'] ?? 'Your account was created through our payment system. A password reset email has been sent to complete your account setup.');
                $response->send(422);
                return;
            }

            if (password_verify($password, $user['password_hash'])) {
                // Check if email is verified
                if ($user['email_verified'] != 1) {
                    StripeLogger::log(StripeLogLevel::INFO, "LOGIN FAILED - Email not verified", [
                        'user_id' => $user['id'],
                        'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
                    ]);

                // Track failed login attempt due to unverified email
                handleFailedLogin($user['id'], 'email', 'Email not verified', ['attempted_email' => $email]);

                // Check if verification token has expired (24 hours)
                $tokenQuery = "SELECT email_verification_sent_at FROM users
                              WHERE id = ?
                              AND email_verification_sent_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                $tokenStmt = mysqli_prepare($link, $tokenQuery);
                mysqli_stmt_bind_param($tokenStmt, "i", $user['id']);
                mysqli_stmt_execute($tokenStmt);
                $tokenResult = mysqli_stmt_get_result($tokenStmt);
                $tokenValid = mysqli_fetch_assoc($tokenResult);

                // If token has expired, include email_not_verified flag so frontend can redirect to resend page
                if (!$tokenValid) {
                    StripeLogger::log(StripeLogLevel::INFO, "LOGIN - Email verification token expired", [
                        'user_id' => $user['id']
                    ]);

                    // Create custom response with additional data for expired token
                    global $selectedLanguage, $errorMessages;
                    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                    http_response_code(403);
                    echo json_encode([
                        'success' => false,
                        'errormsg' => $errorMessages[$lang]['verification_link_expired'],
                        'data' => [
                            'email_not_verified' => true
                        ]
                    ], JSON_PRETTY_PRINT);
                } else {
                    // Token is still valid, just show message without redirect flag
                    global $selectedLanguage, $errorMessages;
                    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                    $response = new ErrorResult($errorMessages[$lang]['email_not_verified']);
                    $response->send(403);
                }
                return;
            }

            StripeLogger::log(StripeLogLevel::INFO, "LOGIN SUCCESS - User authenticated successfully", [
                'user_id' => $user['id'],
                'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
            ]);

            $jwt = createAuthToken($user['id']);

            // Remember me seçeneğine göre refresh token süresi belirle
            if ($remember) {
                $expires_at = time() + (60 * 60 * 24 * 30); // 30 gün
                $refresh_token = createRefreshToken($expires_at);
                $token_type = 'persistent';
            } else {
                $expires_at = null; // Session-based
                $refresh_token = createRefreshToken(); // Session cookie
                $token_type = 'session';
            }

            $user_ip = $_SERVER['REMOTE_ADDR'];
            $userId = $user['id'];

            // Session-based token için veritabanında da session süresi kullan
            $db_expires_at = $remember ? $expires_at : (time() + (60 * 60 * 24)); // Session için 1 gün DB'de

            if (!storeRefreshToken($userId, $refresh_token, $db_expires_at, $user_ip)) {
                StripeLogger::log(StripeLogLevel::ERROR, "LOGIN ERROR - Failed to store refresh token", [
                    'user_id' => $userId
                ]);
                $response = new ErrorResult('Server Error');
                $response->send(500);
                return;
            }

            StripeLogger::log(StripeLogLevel::INFO, "LOGIN COMPLETE - Tokens created and stored successfully", [
                'user_id' => $userId,
                'token_type' => $token_type,
                'expires_at' => $remember ? date('Y-m-d H:i:s', $expires_at) : 'session-based',
                'db_expires_at' => date('Y-m-d H:i:s', $db_expires_at)
            ]);

            // Track successful login
            handleSuccessfulLogin($userId, 'email', ['attempted_email' => $email, 'remember_me' => $remember]);

            $response = new SuccessResult(['token' => $jwt, 'user' => $email]);
            $response->send();
            } else {
                StripeLogger::log(StripeLogLevel::INFO, "LOGIN FAILED - Invalid credentials", [
                    'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
                ]);

                // Track failed login attempt with attempted email for security analysis
                handleFailedLogin(null, 'email', 'Invalid credentials', ['attempted_email' => $email]);

                global $selectedLanguage, $errorMessages;
                $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

                $response = new ErrorResult($errorMessages[$lang]['invalid_credentials']);
                $response->send(422);
            }
        }
    } else {
        StripeLogger::log(StripeLogLevel::ERROR, "LOGIN ERROR - Database error", [
            'error' => mysqli_error($link)
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['database_error']);
        $response->send(500);
    }
}
function logout_user()
{
    global $link;

    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    if (isset($_COOKIE['auth_token2'])) {
        // Safari uyumlu SameSite değeri al
        $sameSite = getSameSiteValue();
        setcookie('auth_token2', '', [
            'expires' => time() - (60 * 60 * 48),
            'path' => '/',
            'domain' => 'coinscout.app',
            'secure' => true,
            'httponly' => false, // Login'deki ayarla tutarlı olmalı
            'samesite' => $sameSite,
        ]); // Geçmişe tarih vererek çerezi sil
        StripeLogger::log(StripeLogLevel::DEBUG, "LOGOUT - Auth token cookie cleared");
    }

    if (isset($_COOKIE['refresh_token'])) {
        $refreshToken = $_COOKIE['refresh_token'];

        // Safari uyumlu SameSite değeri al
        $sameSite = getSameSiteValue();
        setcookie('refresh_token', '', [
            'expires' => time() - (60 * 60 * 48),  // Geçmişe tarih vererek çerezi sil
            'path' => '/',
            'domain' => 'coinscout.app',
            'secure' => true,
            'httponly' => true,
            'samesite' => $sameSite,
        ]);
        $user_ip = $_SERVER['REMOTE_ADDR'];
        StripeLogger::log(StripeLogLevel::DEBUG, "LOGOUT - Refresh token cleared", [
            'token_preview' => substr($refreshToken, 0, 10) . "..."
        ]);
        $query = "UPDATE refresh_tokens SET revoked = 1, reason_revoked= 'logout', revoked_by_ip = ? WHERE token = ?";
        $stmt = mysqli_prepare($link, $query);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "ss", $user_ip, $refreshToken);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        } else {
            StripeLogger::log(StripeLogLevel::ERROR, "LOGOUT ERROR - Failed to prepare SQL statement", [
                'error' => mysqli_error($link)
            ]);
        }
    }
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_unset();
        session_destroy();
    }

    StripeLogger::log(StripeLogLevel::INFO, "LOGOUT SUCCESS - User logged out successfully");

    $response = new SuccessResult([
        'message' => 'Logged out successfully',
        'websocket_disconnect' => true // Frontend'e WebSocket'i kapatması gerektiğini bildir
    ]);
    $response->send();
}
function register_user($email, $password, $username = null, $full_name = null, $country = null, $terms_accepted = false, $captcha_token = null)
{
    global $link;
    global $selectedLanguage;
    global $errorMessages;

    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // Log the start of registration process with detailed information
    StripeLogger::log(StripeLogLevel::INFO, "REGISTRATION STARTED - Processing new user registration", [
        'email' => $email,
        'username_provided' => !empty($username),
        'full_name_provided' => !empty($full_name),
        'country_provided' => !empty($country),
        'terms_accepted' => $terms_accepted ? 'Yes' : 'No',
        'captcha_provided' => !empty($captcha_token) ? 'Yes' : 'No'
    ]);

    // Include email verification functionality
    require_once 'email_verification.php';

    // Include allowed emails whitelist
    require_once 'allowed_emails.php';

    // Include default content for localized strings
    require_once 'language_strings.php';

    // Dil seçimi için varsayılan dil
    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Language settings", [
        'selected_language' => $selectedLanguage,
        'using_language' => $lang
    ]);

    // Check if email is in the allowed list (temporary measure for testing)
    try {
        // Make sure the allowed_emails.php file is included
        if (!function_exists('is_email_allowed')) {
            require_once 'allowed_emails.php';
        }

        // Check if the email is allowed
        $is_allowed = is_email_allowed($email);

        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Email whitelist check result", [
            'email' => $email,
            'is_allowed' => $is_allowed ? 'Yes' : 'No'
        ]);

        if (!$is_allowed) {
            StripeLogger::log(StripeLogLevel::INFO, "REGISTRATION REJECTED - Email not in whitelist", [
                'email' => $email
            ]);
            global $selectedLanguage, $errorMessages;
            $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

            $response = new ErrorResult($errorMessages[$lang]['registration_limited']);
            $response->send(403);
            return;
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Exception in email whitelist check: " . $e->getMessage(), [
            'email' => $email,
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString()
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['registration_error']);
        $response->send(500);
        return;
    }
    // Input validation
    if (empty($email) || empty($password)) {
        $response = new ErrorResult($errorMessages[$lang]['email_password_required']);
        $response->send(400);
        return;
    }
    // Email format validation
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response = new ErrorResult($errorMessages[$lang]['invalid_email_format']);
        $response->send(400);
        return;
    }
    // Enhanced password strength validation
    if (strlen($password) < 8) {
        $response = new ErrorResult($errorMessages[$lang]['password_too_short']);
        $response->send(400);
        return;
    }
    // Check for password complexity
    $uppercase = preg_match('/[A-Z]/', $password);
    $lowercase = preg_match('/[a-z]/', $password);
    $number    = preg_match('/[0-9]/', $password);
    $special   = preg_match('/[^A-Za-z0-9]/', $password);
    if (!$uppercase || !$lowercase || !$number || !$special) {
        $response = new ErrorResult($errorMessages[$lang]['password_complexity_failed']);
        $response->send(400);
        return;
    }
    // Terms acceptance validation
    if (!$terms_accepted) {
        $response = new ErrorResult('You must accept the terms and conditions to register.');
        $response->send(400);
        return;
    }
    // CAPTCHA validation (if implemented)
    if (!empty($captcha_token)) {
        // Verify CAPTCHA token here
        // This is a placeholder for actual CAPTCHA verification
        // You would typically make an API call to your CAPTCHA provider
        // Verify CAPTCHA token using Google reCAPTCHA API
        $captcha_secret = '6LeBrjQrAAAAAOlI6S-oq0efF3ptVYCTh95yt9j6'; // Replace with your reCAPTCHA secret key
        $captcha_response = $captcha_token;
        $captcha_verify_url = 'https://www.google.com/recaptcha/api/siteverify';
        $captcha_data = [
            'secret' => $captcha_secret,
            'response' => $captcha_response
        ];
        $ch = curl_init($captcha_verify_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($captcha_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $captcha_result = curl_exec($ch);
        curl_close($ch);
        $captcha_result_data = json_decode($captcha_result, true);
        $captcha_valid = isset($captcha_result_data['success']) && $captcha_result_data['success'];
        if (!$captcha_valid) {
            $response = new ErrorResult('CAPTCHA verification failed.');
            $response->send(400);
            return;
        }
    }
    // If username is not provided, use the part of email before @
    if (empty($username)) {
        $username = explode('@', $email)[0];
    }
    // Check for existing user with prepared statement
    $query = "SELECT id FROM users WHERE email = ?";
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        $response = new ErrorResult($errorMessages[$lang]['database_error']);
        $response->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt, "s", $email);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    if (mysqli_fetch_assoc($result)) {
        $response = new ErrorResult($errorMessages[$lang]['email_already_registered']);
        $response->send(409);
        return;
    }
    mysqli_begin_transaction($link);
    try {
        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Starting database transaction");

        // Create user
        $password_hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        $registeredVia = RegistrationMethod::EMAIL;
        $created_at = date('Y-m-d H:i:s');
        $terms_accepted_value = $terms_accepted ? 1 : 0;

        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Preparing user insertion", [
            'email' => $email,
            'username' => $username,
            'registration_method' => $registeredVia,
            'created_at' => $created_at
        ]);

        $query = "INSERT INTO users (
            email,
            username,
            password_hash,
            registered_via,
            created_at,
            full_name,
            country,
            terms_accepted,
            terms_accepted_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to prepare user insertion statement", [
                'error' => $error,
                'query' => $query
            ]);
            throw new Exception($errorMessages[$lang]['database_error'] . ' (Error: ' . $error . ')');
        }

        mysqli_stmt_bind_param(
            $stmt,
            "sssssssis",
            $email,
            $username,
            $password_hash,
            $registeredVia,
            $created_at,
            $full_name,
            $country,
            $terms_accepted_value,
            $created_at
        );

        if (!mysqli_stmt_execute($stmt)) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to execute user insertion", [
                'error' => $error,
                'email' => $email,
                'username' => $username
            ]);
            throw new Exception($errorMessages[$lang]['user_registration_failed'] . ' (Error: ' . $error . ')');
        }

        $userId = mysqli_insert_id($link);
        StripeLogger::log(StripeLogLevel::INFO, "REGISTRATION - User record created successfully", [
            'user_id' => $userId,
            'email' => $email
        ]);

        // Get localized content based on selected language
        global $defaultContent;
        $lang = isset($defaultContent[$selectedLanguage]) ? $selectedLanguage : 'en';

        // Create default watchlist with localized name and description
        $watchlistName = $defaultContent[$lang]['watchlist_name'];
        $watchlistDescription = $defaultContent[$lang]['watchlist_description'];

        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Creating default watchlist", [
            'user_id' => $userId,
            'watchlist_name' => $watchlistName
        ]);

        $query = "INSERT INTO watchlists (user_id, name, description, created_at) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to prepare watchlist insertion", [
                'error' => $error,
                'user_id' => $userId
            ]);
            throw new Exception($errorMessages[$lang]['database_error'] . ' (Error: ' . $error . ')');
        }

        mysqli_stmt_bind_param($stmt, "isss", $userId, $watchlistName, $watchlistDescription, $created_at);
        if (!mysqli_stmt_execute($stmt)) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to execute watchlist insertion", [
                'error' => $error,
                'user_id' => $userId
            ]);
            throw new Exception($errorMessages[$lang]['watchlist_creation_failed'] . ' (Error: ' . $error . ')');
        }

        // Create default portfolio with localized name and description
        $portfolioName = $defaultContent[$lang]['portfolio_name'];
        $portfolioDescription = $defaultContent[$lang]['portfolio_description'];

        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Creating default portfolio", [
            'user_id' => $userId,
            'portfolio_name' => $portfolioName
        ]);

        $query = "INSERT INTO portfolio (user_id, name, description, created_at) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to prepare portfolio insertion", [
                'error' => $error,
                'user_id' => $userId
            ]);
            throw new Exception($errorMessages[$lang]['database_error'] . ' (Error: ' . $error . ')');
        }

        mysqli_stmt_bind_param($stmt, "isss", $userId, $portfolioName, $portfolioDescription, $created_at);
        if (!mysqli_stmt_execute($stmt)) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to execute portfolio insertion", [
                'error' => $error,
                'user_id' => $userId
            ]);
            throw new Exception($errorMessages[$lang]['portfolio_creation_failed'] . ' (Error: ' . $error . ')');
        }

        // Generate and store email verification token
        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Generating verification token", [
            'user_id' => $userId
        ]);

        $verification_token = generate_verification_token();
        if (!store_verification_token($userId, $verification_token)) {
            StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION ERROR - Failed to store verification token", [
                'user_id' => $userId
            ]);
            throw new Exception('Failed to generate verification token.');
        }

        // Send verification email
        StripeLogger::log(StripeLogLevel::DEBUG, "REGISTRATION - Sending verification email", [
            'user_id' => $userId,
            'email' => $email
        ]);

        if (!send_verification_email($email, $verification_token, $username)) {
            // Log the error but continue with registration
            StripeLogger::log(StripeLogLevel::WARNING, "REGISTRATION - Failed to send verification email", [
                'email' => $email,
                'username' => $username,
                'user_id' => $userId,
                'reason' => 'Possible issue with email service or configuration'
            ]);
        }

        // Commit transaction
        mysqli_commit($link);
        StripeLogger::log(StripeLogLevel::INFO, "REGISTRATION - Database transaction committed successfully");

        // Log successful registration
        StripeLogger::log(StripeLogLevel::INFO, "REGISTRATION SUCCESS - New user registered", [
            'email' => $email,
            'user_id' => $userId,
            'username' => $username
        ]);

        // Return success response instead of auto-login
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new SuccessResult([
            'message' => $errorMessages[$lang]['registration_successful'],
            'email' => $email,
            'username' => $username
        ]);
        $response->send();
    } catch (Exception $e) {
        mysqli_rollback($link);

        // Log detailed error information
        StripeLogger::log(StripeLogLevel::ERROR, "REGISTRATION FAILED - " . $e->getMessage(), [
            'email' => $email,
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);

        // For critical errors, log at CRITICAL level and send email notification
        if (
            strpos($e->getMessage(), 'database') !== false ||
            strpos($e->getMessage(), 'SQL') !== false ||
            strpos($e->getMessage(), 'connection') !== false
        ) {

            StripeLogger::log(StripeLogLevel::CRITICAL, "REGISTRATION CRITICAL ERROR - Database issue during registration", [
                'error' => $e->getMessage(),
                'email' => $email
            ]);
        }

        $response = new ErrorResult($e->getMessage());
        $response->send(500);
    }
}
function check_password_reset_token($token)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;

    if (empty($token)) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET - Empty token provided");
        $response = new ErrorResult('Invalid password reset token');
        $response->send(400);
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PASSWORD RESET - Checking token validity", [
        'token_preview' => substr($token, 0, 10) . "..."
    ]);

    // Check if the token exists in the password_resets table
    $query = "SELECT pr.user_id, pr.expires_at, u.id as user_exists
              FROM password_resets pr
              JOIN users u ON u.id = pr.user_id
              WHERE pr.token = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET ERROR - Database error", [
            'error' => mysqli_error($link)
        ]);
        $response = new ErrorResult('Database error');
        $response->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmt, "s", $token);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $resetInfo = mysqli_fetch_assoc($result);

    // If token doesn't exist in the database
    if (!$resetInfo) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET - Invalid token", [
            'token_preview' => substr($token, 0, 10) . "..."
        ]);
        $response = new ErrorResult('Invalid password reset token');
        $response->send(400);
        return;
    }

    // Check if user still exists
    if (!$resetInfo['user_exists']) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET - User account not found", [
            'user_id' => $resetInfo['user_id']
        ]);
        $response = new ErrorResult('User account not found');
        $response->send(400);
        return;
    }

    // Check if the token has expired
    $currentDateTime = new DateTime();
    $expiryDateTime = new DateTime($resetInfo['expires_at']);

    if ($currentDateTime > $expiryDateTime) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET - Token expired", [
            'user_id' => $resetInfo['user_id'],
            'token_preview' => substr($token, 0, 10) . "...",
            'expired_at' => $resetInfo['expires_at']
        ]);
        $response = new ErrorResult('Password reset token has expired');
        $response->send(400);
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET - Valid token", [
        'user_id' => $resetInfo['user_id']
    ]);

    // Token is valid
    $response = new SuccessResult("Token is valid");
    $response->send();
}
function generate_reset_token($length = 32)
{
    return bin2hex(random_bytes($length));
}
function forgot_password($email)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;

    if (empty($email)) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET REQUEST - Empty email provided");
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['email_required']);
        $response->send(400);
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PASSWORD RESET REQUEST - Processing request", [
        'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
    ]);

    // Check if user exists
    $query = "SELECT id, username FROM users WHERE email = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "s", $email);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (!$user = mysqli_fetch_assoc($result)) {
        // For security reasons, always return success even if email doesn't exist
        StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET REQUEST - Email not found in database", [
            'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new SuccessResult($errorMessages[$lang]['password_reset_email_sent']);
        $response->send();
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET REQUEST - User found", [
        'user_id' => $user['id'],
        'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
    ]);

    $username = !empty($user['username']) ? $user['username'] : 'Valued Customer';

    // Generate reset token and expiry
    $reset_token = generate_reset_token();
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

    // Store reset token in database
    $query = "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "iss", $user['id'], $reset_token, $expires_at);

    if (!mysqli_stmt_execute($stmt)) {
        StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET REQUEST ERROR - Failed to store token", [
            'user_id' => $user['id'],
            'error' => mysqli_error($link)
        ]);
        $response = new ErrorResult('Failed to process password reset request.');
        $response->send(500);
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PASSWORD RESET REQUEST - Token stored successfully", [
        'user_id' => $user['id'],
        'token_preview' => substr($reset_token, 0, 10) . "...",
        'expires_at' => $expires_at
    ]);

    // Generate reset link
    $reset_link = "https://coinscout.app/reset-password?token=" . $reset_token;
    // Email template
    $message_body = "
<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>CoinScout Password Reset</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .email-header {
            background-color: #172130;
            color: #ffffff;
            padding: 24px 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 15px;
        }
        .email-body {
            padding: 30px;
            color: #334155;
        }
        .email-footer {
            background-color: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
            border-top: 1px solid #e2e8f0;
        }
        h1 {
            color: #1AD5FF;
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: 600;
            font-size: 24px;
        }
        p {
            margin-bottom: 16px;
        }
        .btn {
            display: inline-block;
            background-color: #1AD5FF;
            color: #172130 !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0fc3eb;
        }
        .notice {
            background-color: rgba(26, 213, 255, 0.1);
            border-left: 4px solid #1AD5FF;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .signature {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <div class=\"email-header\">
            <div class=\"logo\">
                <img src=\"https://coinscout.app/assets/Primary-BgPv7JH9.png\" alt=\"CoinScout\" style=\"max-width: 180px; height: auto;\">
            </div>
            <h1 style=\"color: #ffffff; margin: 0;\">Password Reset</h1>
        </div>
        <div class=\"email-body\">
            <p>Hello $username,</p>
            <p>We received a request to reset the password for your CoinScout account.</p>
            <p>If you initiated this request, you can reset your password by clicking the button below:</p>
            <div style=\"text-align: center;\">
                <a href=\"$reset_link\" class=\"btn\">Reset Password</a>
            </div>
            <div class=\"notice\">
                <p style=\"margin: 0;\"><strong>Note:</strong> This link will expire in 1 hour for security purposes.</p>
            </div>
            <p>If you did not request a password reset, you can safely ignore this email.</p>
            <div class=\"signature\">
                <p style=\"margin-bottom: 5px;\">Best regards,</p>
                <p style=\"margin-bottom: 0; font-weight: 600;\">The CoinScout Team</p>
            </div>
        </div>
        <div class=\"email-footer\">
            <p>&copy; 2025 CoinScout. All rights reserved.</p>
            <p>Advanced cryptocurrency investment platform</p>
        </div>
    </div>
</body>
</html>
";
    // Send email
    $mail_result = send_mail($message_body, [$email], "Coinscout Password Reset");

    if ($mail_result) {
        StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET REQUEST - Email sent successfully", [
            'user_id' => $user['id'],
            'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
        ]);
    } else {
        StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET REQUEST ERROR - Failed to send email", [
            'user_id' => $user['id'],
            'email' => substr($email, 0, 2) . '***' . strstr($email, '@')
        ]);
    }

    global $selectedLanguage, $errorMessages;
    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($errorMessages[$lang]['password_reset_email_sent']);
    $response->send();
}
function reset_password($token, $new_password)
{
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;

    if (empty($token) || empty($new_password)) {
        StripeLogger::log(StripeLogLevel::WARNING, "PASSWORD RESET - Missing token or password");
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['token_password_required']);
        $response->send(400);
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PASSWORD RESET - Processing password reset", [
        'token_preview' => substr($token, 0, 10) . "..."
    ]);

    // Check if token exists and is valid
    $query = "SELECT user_id, expires_at FROM password_resets
              WHERE token = ? AND used = 0 AND expires_at > NOW()
              ORDER BY created_at DESC LIMIT 1";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "s", $token);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (!$reset = mysqli_fetch_assoc($result)) {
        StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET - Invalid or expired token", [
            'token_preview' => substr($token, 0, 10) . "..."
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['invalid_or_expired_token']);
        $response->send(400);
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET - Valid token found", [
        'user_id' => $reset['user_id'],
        'token_preview' => substr($token, 0, 10) . "..."
    ]);

    mysqli_begin_transaction($link);
    try {
        // Update password
        $password_hash = password_hash($new_password, PASSWORD_BCRYPT);
        $query = "UPDATE users SET password_hash = ? WHERE id = ?";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "si", $password_hash, $reset['user_id']);

        if (!mysqli_stmt_execute($stmt)) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET ERROR - Failed to update password", [
                'user_id' => $reset['user_id'],
                'error' => $error
            ]);
            throw new Exception('Failed to update password.');
        }

        StripeLogger::log(StripeLogLevel::DEBUG, "PASSWORD RESET - Password updated successfully", [
            'user_id' => $reset['user_id']
        ]);

        // Mark token as used
        $query = "UPDATE password_resets SET used = 1 WHERE token = ?";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "s", $token);

        if (!mysqli_stmt_execute($stmt)) {
            $error = mysqli_error($link);
            StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET ERROR - Failed to mark token as used", [
                'user_id' => $reset['user_id'],
                'token_preview' => substr($token, 0, 10) . "...",
                'error' => $error
            ]);
            throw new Exception('Failed to update token status.');
        }

        mysqli_commit($link);

        StripeLogger::log(StripeLogLevel::INFO, "PASSWORD RESET SUCCESS - Password reset completed", [
            'user_id' => $reset['user_id']
        ]);

        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new SuccessResult($errorMessages[$lang]['password_reset_successful']);
        $response->send();
    } catch (Exception $e) {
        mysqli_rollback($link);

        StripeLogger::log(StripeLogLevel::ERROR, "PASSWORD RESET ERROR - Exception: " . $e->getMessage(), [
            'user_id' => $reset['user_id'],
            'exception' => get_class($e)
        ]);

        $response = new ErrorResult($e->getMessage());
        $response->send(500);
    }
}

function change_password($userId, $current_password, $new_password)
{
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;

    if (empty($current_password) || empty($new_password)) {
        StripeLogger::log(StripeLogLevel::WARNING, "CHANGE PASSWORD - Empty password provided");
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['current_new_password_required']);
        $response->send(400);
        return;
    }

    // Check password length and complexity
    if (strlen($new_password) < 8) {
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['new_password_too_short']);
        $response->send(400);
        return;
    }
    if (password_verify($new_password, $user['password_hash'])) {
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['new_password_same_as_current']);
        $response->send(400);
        return;
    }

    $uppercase = preg_match('/[A-Z]/', $new_password);
    $lowercase = preg_match('/[a-z]/', $new_password);
    $number    = preg_match('/[0-9]/', $new_password);
    $special   = preg_match('/[^A-Za-z0-9]/', $new_password);

    if (!$uppercase || !$lowercase || !$number || !$special) {
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['new_password_complexity']);
        $response->send(400);
        return;
    }

    // Get user's current password hash
    $query = "SELECT password_hash FROM users WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if (!$user) {
        StripeLogger::log(StripeLogLevel::ERROR, "CHANGE PASSWORD - User not found", [
            'user_id' => $userId
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['user_not_found']);
        $response->send(404);
        return;
    }

    // Verify current password
    if (!password_verify($current_password, $user['password_hash'])) {
        StripeLogger::log(StripeLogLevel::WARNING, "CHANGE PASSWORD - Invalid current password", [
            'user_id' => $userId
        ]);
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['current_password_incorrect']);
        $response->send(401);
        return;
    }

    // Update password
    $new_password_hash = password_hash($new_password, PASSWORD_BCRYPT);
    $query = "UPDATE users SET password_hash = ? WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "si", $new_password_hash, $userId);

    if (!mysqli_stmt_execute($stmt)) {
        StripeLogger::log(StripeLogLevel::ERROR, "CHANGE PASSWORD - Failed to update password", [
            'user_id' => $userId,
            'error' => mysqli_error($link)
        ]);
        $response = new ErrorResult('Failed to update password.');
        $response->send(500);
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "CHANGE PASSWORD - Password updated successfully", [
        'user_id' => $userId
    ]);

    global $selectedLanguage, $errorMessages;
    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($errorMessages[$lang]['password_changed_successful']);
    $response->send();
}
