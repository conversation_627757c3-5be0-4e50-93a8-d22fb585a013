<?php
/**
 * IDO Detail Methods
 * Contains methods for retrieving detailed IDO information
 */
// Import language configuration
require_once __DIR__ . '/../language_config.php';
// Import StripeLogger for debugging
require_once __DIR__ . '/../stripe/StripeLogger.php';
/**
 * Get detailed information about an IDO by its ID
 *
 * @param int $id The ID of the IDO
 * @return void
 */
function get_adv_ido_detail_by_id($id)
{
    global $link;
    try {
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Starting get_adv_ido_detail_by_id", [
            'id' => $id,
            'function' => 'get_adv_ido_detail_by_id'
        ]);

        // Include utils for access control
        require_once __DIR__ . '/../utils.php';

        // Get user ID from JWT token
        $userId = getUserIdFromJWT();
        $id = intval($id);

        // Check if user is allowed to view this IDO
        if (!isAllowedIdoForUser($userId, $id)) {
            StripeLogger::log(StripeLogLevel::WARNING, "Unauthorized advanced IDO detail access attempt", [
                'user_id' => $userId,
                'ido_id' => $id,
                'function' => 'get_adv_ido_detail_by_id'
            ]);

            $response = new ErrorResult('Access denied. This IDO is not available in your subscription plan.');
            $response->send(403);
            return;
        }

        StripeLogger::log(StripeLogLevel::INFO, "Authorized advanced IDO detail access", [
            'user_id' => $userId,
            'ido_id' => $id,
            'function' => 'get_adv_ido_detail_by_id'
        ]);

        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Calling getSelectedLanguage()");
        $selectedLanguage = getSelectedLanguage();
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Selected language: " . $selectedLanguage);
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Processed ID: " . $id);
        $query = "
        SELECT
            c.id,
            c.cr_key,
            c.cr_name,
            c.symbol,
            c.image,
            c.description,
            c.total_score,
            c.marketcap_rank,
            c.initial_marketcap,
            c.isactive,
            c.total_supply,
            c.type,
            c.crowdsale_raise,
            c.imc_score,
            c.funding_score,
            c.launchpad_score,
            c.investor_score,
            c.social_score,
            c.fdv,
            c.circulating_supply,
            c.links,
            c.ido_type,
            c.crowdsale_price,
            c.crowdsale_tokenforsale,
            c.crowdsale_ath_roi,
            c.crowdsale_roi,
            c.crowdsale_startdate,
            c.crowdsale_enddate,
            c.crowdsale_alloc_supply,
            c.category_id,
            c.tag_data,
            c.team_data,
            c.contract_data,
            c.lp_name,
            c.lp_image,
            -- Data from ico_projectdata_n8n table (priority data)
            n8n.name as n8n_name,
            n8n.symbol as n8n_symbol,
            n8n.marketcap as n8n_marketcap,
            n8n.fdv as n8n_fdv,
            n8n.circulatingsupply as n8n_circulatingsupply,
            n8n.totalsupply as n8n_totalsupply,
            n8n.allocations as n8n_allocations
        FROM client_ico_coin_list c
        LEFT JOIN ico_projectdata_n8n n8n ON c.cr_key = n8n.cr_key
        WHERE c.id = $id
        ";
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing main query", [
            'query' => $query,
            'id' => $id
        ]);
        $result = mysqli_query($link, $query);
        if (!$result) {
            StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Main query failed: " . mysqli_error($link), [
                'query' => $query,
                'id' => $id,
                'mysql_error' => mysqli_error($link)
            ]);
            throw new Exception("Database query failed: " . mysqli_error($link));
        }
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Main query executed successfully, rows: " . mysqli_num_rows($result));
        if ($result && mysqli_num_rows($result) > 0) {
            $ido = mysqli_fetch_assoc($result);
            // Get the count of IDO watchlists that contain this IDO - for future use
            // Currently not used in the response but keeping the code for future reference
            /*
        $watchlistCountQuery = "SELECT COUNT(DISTINCT watchlist_id) as watchlist_count
                               FROM ido_watchlist_projects
                               WHERE ido_id = $id";
        $watchlistCountResult = mysqli_query($link, $watchlistCountQuery);
        $watchlistCount = 0;
        if ($watchlistCountResult && mysqli_num_rows($watchlistCountResult) > 0) {
            $watchlistCountRow = mysqli_fetch_assoc($watchlistCountResult);
            $watchlistCount = intval($watchlistCountRow['watchlist_count']);
        }
        */
            // Get score status using the existing utility function
            $totalScore = (int)$ido['total_score'];
            $scoreStatus = getScoreStatus($totalScore);
            // Get launchpad data from cr_publicsales_launchpads table
            $launchpadArray = [];
            if (!empty($ido['cr_key'])) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching launchpad data", [
                    'cr_key' => $ido['cr_key']
                ]);
                $cr_key = mysqli_real_escape_string($link, $ido['cr_key']);
                $launchpadQuery = "SELECT name FROM cr_publicsales_launchpads WHERE cr_key = '$cr_key'";
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing launchpad query", [
                    'query' => $launchpadQuery,
                    'cr_key' => $cr_key
                ]);
                $launchpadResult = mysqli_query($link, $launchpadQuery);
                if ($launchpadResult) {
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Launchpad query successful, rows: " . mysqli_num_rows($launchpadResult));
                    while ($launchpadRow = mysqli_fetch_assoc($launchpadResult)) {
                        if (!empty($launchpadRow['name'])) {
                            $launchpadArray[] = $launchpadRow['name'];
                        }
                    }
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded launchpads", ['launchpads' => $launchpadArray]);
                } else {
                    StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Launchpad query failed: " . mysqli_error($link), [
                        'query' => $launchpadQuery,
                        'cr_key' => $cr_key,
                        'mysql_error' => mysqli_error($link)
                    ]);
                }
            }

            // Get category information if category_id is available
            $categoryName = null;
            if (!empty($ido['category_id'])) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching category data", [
                    'category_id' => $ido['category_id']
                ]);
                $categoryId = intval($ido['category_id']);
                $categoryQuery = "SELECT name FROM cr_categories WHERE id = $categoryId";
                $categoryResult = mysqli_query($link, $categoryQuery);
                if ($categoryResult && mysqli_num_rows($categoryResult) > 0) {
                    $categoryRow = mysqli_fetch_assoc($categoryResult);
                    $categoryName = $categoryRow['name'];
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Category found", ['category' => $categoryName]);
                } else {
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Category not found", ['category_id' => $categoryId]);
                }
            }

            // Parse tag_data if available - tag_data contains array of tag IDs
            $tags = [];
            if (!empty($ido['tag_data'])) {
                $tagData = json_decode($ido['tag_data'], true);
                if (is_array($tagData) && !empty($tagData)) {
                    // Convert array to comma-separated string for SQL IN clause
                    $tagIds = array_map('intval', $tagData); // Ensure all values are integers
                    $tagIdsString = implode(',', $tagIds);

                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching tag names", [
                        'tag_ids' => $tagIds
                    ]);

                    $tagQuery = "SELECT cr_name FROM cr_tags WHERE cr_id IN ($tagIdsString)";
                    $tagResult = mysqli_query($link, $tagQuery);

                    if ($tagResult) {
                        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Tag query successful, rows: " . mysqli_num_rows($tagResult));
                        while ($tagRow = mysqli_fetch_assoc($tagResult)) {
                            if (!empty($tagRow['cr_name'])) {
                                $tags[] = $tagRow['cr_name'];
                            }
                        }
                        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded tags", ['tags' => $tags]);
                    } else {
                        StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Tag query failed: " . mysqli_error($link), [
                            'query' => $tagQuery,
                            'tag_ids' => $tagIds,
                            'mysql_error' => mysqli_error($link)
                        ]);
                    }
                }
            }

            // Parse team_data if available
            $teamMembers = [];
            if (!empty($ido['team_data'])) {
                $teamData = json_decode($ido['team_data'], true);
                if (is_array($teamData)) {
                    foreach ($teamData as $member) {
                        $teamMember = [
                            'name' => $member['name'] ?? null,
                            'role' => $member['job'] ?? null,
                            'bio' => null, // Not available in this format
                            'linkedin' => null, // Not available in this format
                            'twitter' => null, // Not available in this format
                            'image' => !empty($member['logo']) ? $member['logo'] : null,
                            'position' => $member['job'] ?? null,
                            'avatar' => !empty($member['logo']) ? $member['logo'] : null
                        ];
                        $teamMembers[] = $teamMember;
                    }
                }
            }

            // Parse contract_data if available
            $contracts = [];
            if (!empty($ido['contract_data'])) {
                $contractData = json_decode($ido['contract_data'], true);
                if (is_array($contractData)) {
                    foreach ($contractData as $contract) {
                        $contractInfo = [
                            'network' => isset($contract['platform']['name']) ? $contract['platform']['name'] : null,
                            'address' => $contract['address'] ?? null,
                            'tokenType' => 'ERC-20', // Default for most tokens, could be enhanced
                            'decimals' => $contract['decimals'] ?? null,
                            'platformKey' => isset($contract['platform']['key']) ? $contract['platform']['key'] : null
                        ];
                        $contracts[] = $contractInfo;
                    }
                }
            }

            // Parse links if available
            $website = null;
            $whitepaper = null;
            $github = null;
            $twitter = null;
            $telegram = null;
            $discord = null;
            $medium = null;
            $facebook = null;
            $youtube = null;
            $linkedin = null;
            $reddit = null;
            $explorer = null;
            $announcement = null;
            // Array to store other link types not explicitly handled
            $otherLinks = [];
            if (!empty($ido['links'])) {
                $linksData = json_decode($ido['links'], true);
                if (is_array($linksData)) {
                    foreach ($linksData as $linkItem) {
                        if (isset($linkItem['type']) && isset($linkItem['value'])) {
                            switch (strtolower($linkItem['type'])) {
                                case 'web':
                                case 'website':
                                    $website = $linkItem['value'];
                                    break;
                                case 'whitepaper':
                                    $whitepaper = $linkItem['value'];
                                    break;
                                case 'github':
                                    $github = $linkItem['value'];
                                    break;
                                case 'twitter':
                                    $twitter = $linkItem['value'];
                                    break;
                                case 'telegram':
                                    $telegram = $linkItem['value'];
                                    break;
                                case 'discord':
                                    $discord = $linkItem['value'];
                                    break;
                                case 'medium':
                                    $medium = $linkItem['value'];
                                    break;
                                case 'facebook':
                                    $facebook = $linkItem['value'];
                                    break;
                                case 'youtube':
                                    $youtube = $linkItem['value'];
                                    break;
                                case 'linkedin':
                                    $linkedin = $linkItem['value'];
                                    break;
                                case 'reddit':
                                    $reddit = $linkItem['value'];
                                    break;
                                case 'explorer':
                                    $explorer = $linkItem['value'];
                                    break;
                                case 'announcement':
                                    $announcement = $linkItem['value'];
                                    break;
                                default:
                                    // Store any other link types
                                    $otherLinks[] = [
                                        'type' => $linkItem['type'],
                                        'url' => $linkItem['value']
                                    ];
                                    break;
                            }
                        }
                    }
                }
            }
            // Get metrics data
            $metrics = [];
            // Get weights from ico_metric_groups
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching weights from ico_metric_groups");
            $weightsQuery = "SELECT id, value FROM ico_metric_groups";
            $weightsResult = mysqli_query($link, $weightsQuery);
            $weights = [];
            if ($weightsResult) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Weights query successful, rows: " . mysqli_num_rows($weightsResult));
                while ($weight = mysqli_fetch_assoc($weightsResult)) {
                    $weights[$weight['id']] = $weight['value'];
                }
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded weights", ['weights' => $weights]);
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Weight query failed: " . mysqli_error($link), [
                    'query' => $weightsQuery,
                    'mysql_error' => mysqli_error($link)
                ]);
            }
            // Get names and descriptions from ico_metric_subgroups based on selected language
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Building language column for descriptions");
            $languageColumn = "description_" . strtoupper($selectedLanguage);
            $languageColumn = mysqli_real_escape_string($link, $languageColumn);
            $languageNameColumn = "name_" . strtoupper($selectedLanguage);
            $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);
            $languageWhatColumn = "what_are_we_scoring_" . strtoupper($selectedLanguage);
            $languageWhatColumn = mysqli_real_escape_string($link, $languageWhatColumn);
            $languageWhyColumn = "why_is_this_important_" . strtoupper($selectedLanguage);
            $languageWhyColumn = mysqli_real_escape_string($link, $languageWhyColumn);
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Language columns", [
                'description_column' => $languageColumn,
                'what_column' => $languageWhatColumn,
                'why_column' => $languageWhyColumn
            ]);
            $descriptionsQuery = "SELECT metric_group, `$languageNameColumn` as name, `$languageColumn` as description, `$languageWhatColumn` as what_are_we_scoring, `$languageWhyColumn` as why_is_this_important FROM ico_metric_subgroups WHERE isactive = 1";
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing descriptions query", [
                'query' => $descriptionsQuery,
                'language_column' => $languageColumn
            ]);
            $descriptionsResult = mysqli_query($link, $descriptionsQuery);
            $metricInfo = [];
            if ($descriptionsResult) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Descriptions query successful, rows: " . mysqli_num_rows($descriptionsResult));
                while ($row = mysqli_fetch_assoc($descriptionsResult)) {
                    $metricInfo[$row['metric_group']] = [
                        'name' => $row['name'],
                        'description' => $row['description'],
                        'what_are_we_scoring' => $row['what_are_we_scoring'],
                        'why_is_this_important' => $row['why_is_this_important']
                    ];
                }
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded metric info", ['metric_info' => $metricInfo]);
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Metric info query failed: " . mysqli_error($link), [
                    'query' => $descriptionsQuery,
                    'language_column' => $languageColumn,
                    'mysql_error' => mysqli_error($link)
                ]);
            }
            // Add IMC Score
            $metrics[] = [
                'name' => isset($metricInfo[1]['name']) ? $metricInfo[1]['name'] : 'IMC Score',
                'description' => isset($metricInfo[1]['description']) ? $metricInfo[1]['description'] : 'Initial Market Cap Score',
                'what_are_we_scoring' => isset($metricInfo[1]['what_are_we_scoring']) ? $metricInfo[1]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[1]['why_is_this_important']) ? $metricInfo[1]['why_is_this_important'] : null,
                'weight' => isset($weights[1]) ? floatval($weights[1]) : 0,
                'score' => floatval($ido['imc_score'])
            ];
            // Add Funding Score
            $metrics[] = [
                'name' => isset($metricInfo[2]['name']) ? $metricInfo[2]['name'] : 'Funding Score',
                'description' => isset($metricInfo[2]['description']) ? $metricInfo[2]['description'] : 'Funding Score',
                'what_are_we_scoring' => isset($metricInfo[2]['what_are_we_scoring']) ? $metricInfo[2]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[2]['why_is_this_important']) ? $metricInfo[2]['why_is_this_important'] : null,
                'weight' => isset($weights[2]) ? floatval($weights[2]) : 0,
                'score' => floatval($ido['funding_score'])
            ];
            // Add Launchpad Score
            $metrics[] = [
                'name' => isset($metricInfo[3]['name']) ? $metricInfo[3]['name'] : 'Launchpad Score',
                'description' => isset($metricInfo[3]['description']) ? $metricInfo[3]['description'] : 'Launchpad Score',
                'what_are_we_scoring' => isset($metricInfo[3]['what_are_we_scoring']) ? $metricInfo[3]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[3]['why_is_this_important']) ? $metricInfo[3]['why_is_this_important'] : null,
                'weight' => isset($weights[3]) ? floatval($weights[3]) : 0,
                'score' => floatval($ido['launchpad_score'])
            ];
            // Add Investor Score
            $metrics[] = [
                'name' => isset($metricInfo[4]['name']) ? $metricInfo[4]['name'] : 'Investor Score',
                'description' => isset($metricInfo[4]['description']) ? $metricInfo[4]['description'] : 'Investor Score',
                'what_are_we_scoring' => isset($metricInfo[4]['what_are_we_scoring']) ? $metricInfo[4]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[4]['why_is_this_important']) ? $metricInfo[4]['why_is_this_important'] : null,
                'weight' => isset($weights[4]) ? floatval($weights[4]) : 0,
                'score' => floatval($ido['investor_score'])
            ];
            // Add Social Score
            $metrics[] = [
                'name' => isset($metricInfo[5]['name']) ? $metricInfo[5]['name'] : 'Social Score',
                'description' => isset($metricInfo[5]['description']) ? $metricInfo[5]['description'] : 'Social Score',
                'what_are_we_scoring' => isset($metricInfo[5]['what_are_we_scoring']) ? $metricInfo[5]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[5]['why_is_this_important']) ? $metricInfo[5]['why_is_this_important'] : null,
                'weight' => isset($weights[5]) ? floatval($weights[5]) : 0,
                'score' => floatval($ido['social_score'])
            ];
            // Use data from ico_projectdata_n8n table when available (priority), otherwise use original data
            $projectName = !empty($ido['n8n_name']) ? $ido['n8n_name'] : $ido['cr_name'];
            $projectSymbol = !empty($ido['n8n_symbol']) ? $ido['n8n_symbol'] : $ido['symbol'];
            $projectMarketCap = !empty($ido['n8n_marketcap']) ? (float)$ido['n8n_marketcap'] : (!empty($ido['initial_marketcap']) ? (float)$ido['initial_marketcap'] : null);
            $projectFdv = !empty($ido['n8n_fdv']) ? (float)$ido['n8n_fdv'] : (!empty($ido['fdv']) ? (float)$ido['fdv'] : null);
            $projectCirculatingSupply = !empty($ido['n8n_circulatingsupply']) ? (float)$ido['n8n_circulatingsupply'] : (!empty($ido['circulating_supply']) ? (float)$ido['circulating_supply'] : null);
            $projectTotalSupply = !empty($ido['n8n_totalsupply']) ? (float)$ido['n8n_totalsupply'] : (!empty($ido['total_supply']) ? (float)$ido['total_supply'] : null);

            // Parse allocations from n8n table if available - use original structure
            $n8nAllocations = null;
            if (!empty($ido['n8n_allocations'])) {
                $n8nAllocations = json_decode($ido['n8n_allocations'], true);
            }

            // Format the response according to the requested structure
            $formattedResponse = [
                "id" => (string)$ido['id'],
                "name" => $projectName,
                "symbol" => $projectSymbol,
                "description" => $ido['description'],
                "logo" => $ido['image'],
                "status" => $scoreStatus,
                "category" => $categoryName,
                "saleType" => !empty($ido['ido_type']) ? $ido['ido_type'] : (!empty($ido['type']) ? $ido['type'] : null),
                "launchpad" => !empty($launchpadArray) ? $launchpadArray : null,
                "idoPrice" => !empty($ido['crowdsale_price']) ? (float)$ido['crowdsale_price'] : null,
                "currentPrice" => null,
                "priceChange" => null,
                "btcPrice" => null,
                "totalSupply" => $projectTotalSupply,
                "initialCirculatingSupply" => $projectCirculatingSupply,
                "initialMarketCap" => $projectMarketCap,
                "currentMarketCap" => $projectMarketCap, // Use same value for current market cap
                "fdv" => $projectFdv,
                // Project links are now only in socialLinks array
                "score" => $totalScore,
                "metrics" => $metrics,
                // Token allocation data - use n8n data when available (original structure)
                "allocation" => [
                    "teamAndAdvisors" => null,
                    "publicSale" => !empty($ido['crowdsale_alloc_supply']) ? (float)$ido['crowdsale_alloc_supply'] : null,
                    "privateSale" => null,
                    "ecosystem" => null,
                    "liquidity" => null,
                    "treasury" => null,
                    "marketing" => null,
                    "maxSupply" => $projectTotalSupply,
                    "allocations" => is_array($n8nAllocations) ? $n8nAllocations : [
                        ["name" => "Team & Advisors", "value" => null, "percentage" => null],
                        ["name" => "Public Sale", "value" => !empty($ido['crowdsale_tokenforsale']) ? (float)$ido['crowdsale_tokenforsale'] : null, "percentage" => !empty($ido['crowdsale_alloc_supply']) ? (float)$ido['crowdsale_alloc_supply'] : null],
                        ["name" => "Private Sale", "value" => null, "percentage" => null],
                        ["name" => "Ecosystem", "value" => null, "percentage" => null],
                        ["name" => "Liquidity", "value" => null, "percentage" => null],
                        ["name" => "Treasury", "value" => null, "percentage" => null],
                        ["name" => "Marketing", "value" => null, "percentage" => null]
                    ]
                ],
                // Token distribution data
                "distribution" => [
                    "released" => null,
                    "locked" => null,
                    "nextRelease" => null,
                    "nextUnlock" => null,
                    "untracked" => null
                ],
                // Unlock schedule
                "unlockEvents" => [
                    [
                        "date" => null,
                        "percentage" => null,
                        "description" => null,
                        "tokens" => null,
                        "daysLeft" => null,
                        "unlockAmount" => null,
                        "unlockPercentage" => null,
                        "maxSupply" => null,
                        "allocations" => null
                    ]
                ],
                // Next token unlock event
                "nextUnlockEvent" => [
                    "date" => null,
                    "percentage" => null,
                    "description" => null,
                    "tokens" => null,
                    "daysRemaining" => null,
                    "unlockAmount" => null,
                    "unlockPercentage" => null
                ],
                // Vesting info links
                "vestingInfoLinks" => [
                    [
                        "title" => null,
                        "url" => null,
                        "label" => null
                    ]
                ],
                // Funding information
                "funding" => [
                    "total" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                    "totalRaised" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                    "projectName" => $projectName,
                    "description" => null,
                    "publicSale" => !empty($ido['crowdsale_tokenforsale']) ? (float)$ido['crowdsale_tokenforsale'] : null,
                    "fundingRounds" => null,
                    "rounds" => [
                        [
                            "name" => "Public Sale",
                            "amount" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                            "date" => !empty($ido['crowdsale_startdate']) ? date('Y-m-d', $ido['crowdsale_startdate']) : null
                        ]
                    ],
                    "valuations" => [
                        "seed" => null,
                        "private" => null,
                        "public" => $projectMarketCap
                    ],
                    "saleStartDate" => !empty($ido['crowdsale_startdate']) ? $ido['crowdsale_startdate'] : null,
                    "saleEndDate" => !empty($ido['crowdsale_enddate']) ? $ido['crowdsale_enddate'] : null,
                    "athRoi" => !empty($ido['crowdsale_ath_roi']) ? (float)$ido['crowdsale_ath_roi'] : null,
                    "currentRoi" => !empty($ido['crowdsale_roi']) ? (float)$ido['crowdsale_roi'] : null
                ],
                // Price projections
                "priceProjections" => [
                    [
                        "scenario" => "Conservative",
                        "price" => null,
                        "marketCap" => null,
                        "multiplier" => null,
                        "usdPrice" => null,
                        "btcPrice" => null
                    ]
                ],
                // Team information
                "team" => !empty($teamMembers) ? $teamMembers : [
                    [
                        "name" => null,
                        "role" => null,
                        "bio" => null,
                        "linkedin" => null,
                        "twitter" => null,
                        "image" => null,
                        "position" => null,
                        "avatar" => null
                    ]
                ],
                // Investors information
                "investors" => [
                    [
                        "name" => null,
                        "type" => null,
                        "amount" => null,
                        "logo" => null
                    ]
                ],
                // Contract information
                "contracts" => !empty($contracts) ? $contracts : [
                    [
                        "network" => null,
                        "address" => null,
                        "tokenType" => null
                    ]
                ],
                // Project tags
                "tags" => !empty($tags) ? $tags : null,
                // Launchpad information
                "launchpadInfo" => [
                    "name" => !empty($ido['lp_name']) ? $ido['lp_name'] : null,
                    "image" => !empty($ido['lp_image']) ? $ido['lp_image'] : null
                ],
                // Social links (additional)
                "socialLinks" => [
                    ["platform" => "Website", "url" => $website],
                    ["platform" => "Whitepaper", "url" => $whitepaper],
                    ["platform" => "GitHub", "url" => $github],
                    ["platform" => "Twitter", "url" => $twitter],
                    ["platform" => "Telegram", "url" => $telegram],
                    ["platform" => "Discord", "url" => $discord],
                    ["platform" => "Medium", "url" => $medium],
                    ["platform" => "Facebook", "url" => $facebook],
                    ["platform" => "YouTube", "url" => $youtube],
                    ["platform" => "LinkedIn", "url" => $linkedin],
                    ["platform" => "Reddit", "url" => $reddit],
                    ["platform" => "Explorer", "url" => $explorer],
                    ["platform" => "Announcement", "url" => $announcement]
                ],
                // Other links not categorized above
                "otherLinks" => $otherLinks
            ];
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Creating success response");
            $response = new SuccessResult($formattedResponse);
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Sending success response");
            $response->send();
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "IDO DETAIL - IDO not found", ['id' => $id]);
            $response = new ErrorResult('IDO not found.');
            $response->send(404);
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::CRITICAL, "IDO DETAIL - Exception in get_adv_ido_detail_by_id: " . $e->getMessage(), [
            'id' => $id,
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        $response = new ErrorResult('Internal server error occurred.');
        $response->send(500);
    }
}
/**
 * Get simplified information about an IDO by its ID
 *
 * @param int $id The ID of the IDO
 * @return void
 */
function get_ido_detail_by_id($id)
{
    global $link;
    try {
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Starting get_ido_detail_by_id", [
            'id' => $id,
            'function' => 'get_ido_detail_by_id'
        ]);

        // Include utils for access control
        require_once __DIR__ . '/../utils.php';

        // Get user ID from JWT token
        $userId = getUserIdFromJWT();
        $id = intval($id);

        // Check if user is allowed to view this IDO
        if (!isAllowedIdoForUser($userId, $id)) {
            StripeLogger::log(StripeLogLevel::WARNING, "Unauthorized IDO detail access attempt", [
                'user_id' => $userId,
                'ido_id' => $id,
                'function' => 'get_ido_detail_by_id'
            ]);

            $response = new ErrorResult('Access denied. This IDO is not available in your subscription plan.');
            $response->send(403);
            return;
        }

        StripeLogger::log(StripeLogLevel::INFO, "Authorized IDO detail access", [
            'user_id' => $userId,
            'ido_id' => $id,
            'function' => 'get_ido_detail_by_id'
        ]);

        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Calling getSelectedLanguage()");
        $selectedLanguage = getSelectedLanguage();
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Selected language: " . $selectedLanguage);
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Processed ID: " . $id);
        $query = "
        SELECT
            c.id,
            c.cr_key,
            c.cr_name,
            c.symbol,
            c.image,
            c.description,
            c.total_score,
            c.marketcap_rank,
            c.initial_marketcap,
            c.isactive,
            c.total_supply,
            c.type,
            c.crowdsale_raise,
            c.imc_score,
            c.funding_score,
            c.launchpad_score,
            c.investor_score,
            c.social_score,
            c.fdv,
            c.circulating_supply,
            c.links,
            c.ido_type,
            c.crowdsale_price,
            c.crowdsale_tokenforsale,
            c.crowdsale_ath_roi,
            c.crowdsale_roi,
            c.crowdsale_startdate,
            c.crowdsale_enddate,
            c.crowdsale_alloc_supply,
            c.category_id,
            c.tag_data,
            c.team_data,
            c.contract_data,
            c.lp_name,
            c.lp_image,
            -- Data from ico_projectdata_n8n table (priority data)
            n8n.name as n8n_name,
            n8n.symbol as n8n_symbol,
            n8n.marketcap as n8n_marketcap,
            n8n.fdv as n8n_fdv,
            n8n.circulatingsupply as n8n_circulatingsupply,
            n8n.totalsupply as n8n_totalsupply,
            n8n.allocations as n8n_allocations
        FROM client_ico_coin_list c
        LEFT JOIN ico_projectdata_n8n n8n ON c.cr_key = n8n.cr_key
        WHERE c.id = $id
        ";
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing main query (simple)", [
            'query' => $query,
            'id' => $id
        ]);
        $result = mysqli_query($link, $query);
        if (!$result) {
            StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Main query failed (simple): " . mysqli_error($link), [
                'query' => $query,
                'id' => $id,
                'mysql_error' => mysqli_error($link)
            ]);
            throw new Exception("Database query failed: " . mysqli_error($link));
        }
        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Main query executed successfully (simple), rows: " . mysqli_num_rows($result));
        if ($result && mysqli_num_rows($result) > 0) {
            $ido = mysqli_fetch_assoc($result);
            // Get score status using the existing utility function
            $totalScore = (int)$ido['total_score'];
            $scoreStatus = getScoreStatus($totalScore);
            // Get launchpad data from cr_publicsales_launchpads table
            $launchpadArray = [];
            if (!empty($ido['cr_key'])) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching launchpad data (simple)", [
                    'cr_key' => $ido['cr_key']
                ]);
                $cr_key = mysqli_real_escape_string($link, $ido['cr_key']);
                $launchpadQuery = "SELECT name FROM cr_publicsales_launchpads WHERE cr_key = '$cr_key'";
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing launchpad query (simple)", [
                    'query' => $launchpadQuery,
                    'cr_key' => $cr_key
                ]);
                $launchpadResult = mysqli_query($link, $launchpadQuery);
                if ($launchpadResult) {
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Launchpad query successful (simple), rows: " . mysqli_num_rows($launchpadResult));
                    while ($launchpadRow = mysqli_fetch_assoc($launchpadResult)) {
                        if (!empty($launchpadRow['name'])) {
                            $launchpadArray[] = $launchpadRow['name'];
                        }
                    }
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded launchpads (simple)", ['launchpads' => $launchpadArray]);
                } else {
                    StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Launchpad query failed (simple): " . mysqli_error($link), [
                        'query' => $launchpadQuery,
                        'cr_key' => $cr_key,
                        'mysql_error' => mysqli_error($link)
                    ]);
                }
            }

            // Get category information if category_id is available
            $categoryName = null;
            if (!empty($ido['category_id'])) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching category data (simple)", [
                    'category_id' => $ido['category_id']
                ]);
                $categoryId = intval($ido['category_id']);
                $categoryQuery = "SELECT name FROM cr_categories WHERE id = $categoryId";
                $categoryResult = mysqli_query($link, $categoryQuery);
                if ($categoryResult && mysqli_num_rows($categoryResult) > 0) {
                    $categoryRow = mysqli_fetch_assoc($categoryResult);
                    $categoryName = $categoryRow['name'];
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Category found (simple)", ['category' => $categoryName]);
                } else {
                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Category not found (simple)", ['category_id' => $categoryId]);
                }
            }

            // Parse tag_data if available - tag_data contains array of tag IDs
            $tags = [];
            if (!empty($ido['tag_data'])) {
                $tagData = json_decode($ido['tag_data'], true);
                if (is_array($tagData) && !empty($tagData)) {
                    // Convert array to comma-separated string for SQL IN clause
                    $tagIds = array_map('intval', $tagData); // Ensure all values are integers
                    $tagIdsString = implode(',', $tagIds);

                    StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching tag names (simple)", [
                        'tag_ids' => $tagIds
                    ]);

                    $tagQuery = "SELECT cr_name FROM cr_tags WHERE cr_id IN ($tagIdsString)";
                    $tagResult = mysqli_query($link, $tagQuery);

                    if ($tagResult) {
                        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Tag query successful (simple), rows: " . mysqli_num_rows($tagResult));
                        while ($tagRow = mysqli_fetch_assoc($tagResult)) {
                            if (!empty($tagRow['cr_name'])) {
                                $tags[] = $tagRow['cr_name'];
                            }
                        }
                        StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded tags (simple)", ['tags' => $tags]);
                    } else {
                        StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Tag query failed (simple): " . mysqli_error($link), [
                            'query' => $tagQuery,
                            'tag_ids' => $tagIds,
                            'mysql_error' => mysqli_error($link)
                        ]);
                    }
                }
            }

            // Parse team_data if available (simplified for basic version)
            $teamMembers = [];
            if (!empty($ido['team_data'])) {
                $teamData = json_decode($ido['team_data'], true);
                if (is_array($teamData)) {
                    foreach ($teamData as $member) {
                        $teamMember = [
                            'name' => $member['name'] ?? null,
                            'role' => $member['job'] ?? null,
                            'image' => !empty($member['logo']) ? $member['logo'] : null
                        ];
                        $teamMembers[] = $teamMember;
                    }
                }
            }

            // Parse contract_data if available (simplified for basic version)
            $contracts = [];
            if (!empty($ido['contract_data'])) {
                $contractData = json_decode($ido['contract_data'], true);
                if (is_array($contractData)) {
                    foreach ($contractData as $contract) {
                        $contractInfo = [
                            'network' => isset($contract['platform']['name']) ? $contract['platform']['name'] : null,
                            'address' => $contract['address'] ?? null
                        ];
                        $contracts[] = $contractInfo;
                    }
                }
            }

            // Parse links if available
            $website = null;
            $whitepaper = null;
            $github = null;
            $twitter = null;
            $telegram = null;
            $discord = null;
            $medium = null;
            $facebook = null;
            $youtube = null;
            $linkedin = null;
            $reddit = null;
            $explorer = null;
            $announcement = null;
            // Array to store other link types not explicitly handled
            $otherLinks = [];
            if (!empty($ido['links'])) {
                $linksData = json_decode($ido['links'], true);
                if (is_array($linksData)) {
                    foreach ($linksData as $linkItem) {
                        if (isset($linkItem['type']) && isset($linkItem['value'])) {
                            switch (strtolower($linkItem['type'])) {
                                case 'web':
                                case 'website':
                                    $website = $linkItem['value'];
                                    break;
                                case 'whitepaper':
                                    $whitepaper = $linkItem['value'];
                                    break;
                                case 'github':
                                    $github = $linkItem['value'];
                                    break;
                                case 'twitter':
                                    $twitter = $linkItem['value'];
                                    break;
                                case 'telegram':
                                    $telegram = $linkItem['value'];
                                    break;
                                case 'discord':
                                    $discord = $linkItem['value'];
                                    break;
                                case 'medium':
                                    $medium = $linkItem['value'];
                                    break;
                                case 'facebook':
                                    $facebook = $linkItem['value'];
                                    break;
                                case 'youtube':
                                    $youtube = $linkItem['value'];
                                    break;
                                case 'linkedin':
                                    $linkedin = $linkItem['value'];
                                    break;
                                case 'reddit':
                                    $reddit = $linkItem['value'];
                                    break;
                                case 'explorer':
                                    $explorer = $linkItem['value'];
                                    break;
                                case 'announcement':
                                    $announcement = $linkItem['value'];
                                    break;
                                default:
                                    // Store any other link types
                                    $otherLinks[] = [
                                        'type' => $linkItem['type'],
                                        'url' => $linkItem['value']
                                    ];
                                    break;
                            }
                        }
                    }
                }
            }
            // Price information - using actual data where available
            $idoPrice = !empty($ido['crowdsale_price']) ? (float)$ido['crowdsale_price'] : null;
            $currentPrice = null;
            $priceChange = null;
            // Get weights from ico_metric_groups
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Fetching weights from ico_metric_groups (simple)");
            $weightsQuery = "SELECT id, value FROM ico_metric_groups";
            $weightsResult = mysqli_query($link, $weightsQuery);
            $weights = [];
            if ($weightsResult) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Weights query successful (simple), rows: " . mysqli_num_rows($weightsResult));
                while ($weight = mysqli_fetch_assoc($weightsResult)) {
                    $weights[$weight['id']] = $weight['value'];
                }
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded weights (simple)", ['weights' => $weights]);
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Weight query failed (simple): " . mysqli_error($link), [
                    'query' => $weightsQuery,
                    'mysql_error' => mysqli_error($link)
                ]);
            }
            // Get metric names and descriptions from ico_metric_subgroups
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Building language column for descriptions (simple)");
            $languageColumn = "description_" . strtoupper($selectedLanguage);
            $languageColumn = mysqli_real_escape_string($link, $languageColumn);
            $languageNameColumn = "name_" . strtoupper($selectedLanguage);
            $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);
            $languageWhatColumn = "what_are_we_scoring_" . strtoupper($selectedLanguage);
            $languageWhatColumn = mysqli_real_escape_string($link, $languageWhatColumn);
            $languageWhyColumn = "why_is_this_important_" . strtoupper($selectedLanguage);
            $languageWhyColumn = mysqli_real_escape_string($link, $languageWhyColumn);
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Language columns (simple)", [
                'description_column' => $languageColumn,
                'what_column' => $languageWhatColumn,
                'why_column' => $languageWhyColumn
            ]);
            $metricInfoQuery = "SELECT metric_group, `$languageNameColumn` as name, `$languageColumn` as description, `$languageWhatColumn` as what_are_we_scoring, `$languageWhyColumn` as why_is_this_important FROM ico_metric_subgroups WHERE isactive = 1";
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Executing metric info query (simple)", [
                'query' => $metricInfoQuery,
                'language_column' => $languageColumn
            ]);
            $metricInfoResult = mysqli_query($link, $metricInfoQuery);
            $metricInfo = [];
            if ($metricInfoResult) {
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Metric info query successful (simple), rows: " . mysqli_num_rows($metricInfoResult));
                while ($row = mysqli_fetch_assoc($metricInfoResult)) {
                    $metricInfo[$row['metric_group']] = [
                        'name' => $row['name'],
                        'description' => $row['description'],
                        'what_are_we_scoring' => $row['what_are_we_scoring'],
                        'why_is_this_important' => $row['why_is_this_important']
                    ];
                }
                StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Loaded metric info (simple)", ['metric_info' => $metricInfo]);
            } else {
                StripeLogger::log(StripeLogLevel::ERROR, "IDO DETAIL - Metric info query failed (simple): " . mysqli_error($link), [
                    'query' => $metricInfoQuery,
                    'language_column' => $languageColumn,
                    'mysql_error' => mysqli_error($link)
                ]);
            }
            // Format metrics data
            $metrics = [];
            // Add IMC Score
            $metrics[] = [
                'name' => isset($metricInfo[1]['name']) ? $metricInfo[1]['name'] : 'IMC Score',
                'description' => isset($metricInfo[1]['description']) ? $metricInfo[1]['description'] : 'Initial Market Cap Score',
                'what_are_we_scoring' => isset($metricInfo[1]['what_are_we_scoring']) ? $metricInfo[1]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[1]['why_is_this_important']) ? $metricInfo[1]['why_is_this_important'] : null,
                'weight' => isset($weights[1]) ? floatval($weights[1]) : 0,
                'score' => floatval($ido['imc_score'])
            ];
            // Add Funding Score
            $metrics[] = [
                'name' => isset($metricInfo[2]['name']) ? $metricInfo[2]['name'] : 'Funding Score',
                'description' => isset($metricInfo[2]['description']) ? $metricInfo[2]['description'] : 'Funding Score',
                'what_are_we_scoring' => isset($metricInfo[2]['what_are_we_scoring']) ? $metricInfo[2]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[2]['why_is_this_important']) ? $metricInfo[2]['why_is_this_important'] : null,
                'weight' => isset($weights[2]) ? floatval($weights[2]) : 0,
                'score' => floatval($ido['funding_score'])
            ];
            // Add Launchpad Score
            $metrics[] = [
                'name' => isset($metricInfo[3]['name']) ? $metricInfo[3]['name'] : 'Launchpad Score',
                'description' => isset($metricInfo[3]['description']) ? $metricInfo[3]['description'] : 'Launchpad Score',
                'what_are_we_scoring' => isset($metricInfo[3]['what_are_we_scoring']) ? $metricInfo[3]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[3]['why_is_this_important']) ? $metricInfo[3]['why_is_this_important'] : null,
                'weight' => isset($weights[3]) ? floatval($weights[3]) : 0,
                'score' => floatval($ido['launchpad_score'])
            ];
            // Add Investor Score
            $metrics[] = [
                'name' => isset($metricInfo[4]['name']) ? $metricInfo[4]['name'] : 'Investor Score',
                'description' => isset($metricInfo[4]['description']) ? $metricInfo[4]['description'] : 'Investor Score',
                'what_are_we_scoring' => isset($metricInfo[4]['what_are_we_scoring']) ? $metricInfo[4]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[4]['why_is_this_important']) ? $metricInfo[4]['why_is_this_important'] : null,
                'weight' => isset($weights[4]) ? floatval($weights[4]) : 0,
                'score' => floatval($ido['investor_score'])
            ];
            // Add Social Score
            $metrics[] = [
                'name' => isset($metricInfo[5]['name']) ? $metricInfo[5]['name'] : 'Social Score',
                'description' => isset($metricInfo[5]['description']) ? $metricInfo[5]['description'] : 'Social Score',
                'what_are_we_scoring' => isset($metricInfo[5]['what_are_we_scoring']) ? $metricInfo[5]['what_are_we_scoring'] : null,
                'why_is_this_important' => isset($metricInfo[5]['why_is_this_important']) ? $metricInfo[5]['why_is_this_important'] : null,
                'weight' => isset($weights[5]) ? floatval($weights[5]) : 0,
                'score' => floatval($ido['social_score'])
            ];

            // Use data from ico_projectdata_n8n table when available (priority), otherwise use original data
            $projectName = !empty($ido['n8n_name']) ? $ido['n8n_name'] : $ido['cr_name'];
            $projectSymbol = !empty($ido['n8n_symbol']) ? $ido['n8n_symbol'] : $ido['symbol'];
            $projectMarketCap = !empty($ido['n8n_marketcap']) ? (float)$ido['n8n_marketcap'] : (!empty($ido['initial_marketcap']) ? (float)$ido['initial_marketcap'] : null);
            $projectFdv = !empty($ido['n8n_fdv']) ? (float)$ido['n8n_fdv'] : (!empty($ido['fdv']) ? (float)$ido['fdv'] : null);
            $projectCirculatingSupply = !empty($ido['n8n_circulatingsupply']) ? (float)$ido['n8n_circulatingsupply'] : (!empty($ido['circulating_supply']) ? (float)$ido['circulating_supply'] : null);
            $projectTotalSupply = !empty($ido['n8n_totalsupply']) ? (float)$ido['n8n_totalsupply'] : (!empty($ido['total_supply']) ? (float)$ido['total_supply'] : null);

            // Parse allocations from n8n table if available - use original structure
            $n8nAllocations = null;
            if (!empty($ido['n8n_allocations'])) {
                $n8nAllocations = json_decode($ido['n8n_allocations'], true);
            }

            // Token distribution values are now fixed for consistency
            // Format the response according to the requested structure
            $formattedResponse = [
                "id" => (string)$ido['id'],
                "name" => $projectName,
                "symbol" => $projectSymbol,
                "description" => $ido['description'],
                "logo" => $ido['image'],
                "status" => $scoreStatus,
                "category" => $categoryName,
                "saleType" => !empty($ido['ido_type']) ? $ido['ido_type'] : (!empty($ido['type']) ? $ido['type'] : null),
                "launchpad" => !empty($launchpadArray) ? $launchpadArray : null,
                "idoPrice" => $idoPrice,
                "currentPrice" => $currentPrice,
                "priceChange" => $priceChange,
                "totalSupply" => $projectTotalSupply,
                "initialCirculatingSupply" => $projectCirculatingSupply,
                "initialMarketCap" => $projectMarketCap,
                "currentMarketCap" => $projectMarketCap, // Use same value for current market cap
                "fdv" => $projectFdv,
                "score" => $totalScore,
                // Metrics data
                "metrics" => $metrics,
                // Token distribution data
                "distribution" => [
                    "released" => null,
                    "locked" => null,
                    "nextRelease" => null,
                    "nextUnlock" => null,
                    "untracked" => null
                ],
                // Social links (additional)
                "socialLinks" => [
                    ["platform" => "Website", "url" => $website],
                    ["platform" => "Whitepaper", "url" => $whitepaper],
                    ["platform" => "GitHub", "url" => $github],
                    ["platform" => "Twitter", "url" => $twitter],
                    ["platform" => "Telegram", "url" => $telegram],
                    ["platform" => "Discord", "url" => $discord],
                    ["platform" => "Medium", "url" => $medium],
                    ["platform" => "Facebook", "url" => $facebook],
                    ["platform" => "YouTube", "url" => $youtube],
                    ["platform" => "LinkedIn", "url" => $linkedin],
                    ["platform" => "Reddit", "url" => $reddit],
                    ["platform" => "Explorer", "url" => $explorer],
                    ["platform" => "Announcement", "url" => $announcement]
                ],
                // Other links not categorized above
                "otherLinks" => $otherLinks,
                // Simplified versions of other fields from get_adv_ido_detail_by_id
                "allocation" => [
                    "teamAndAdvisors" => null,
                    "publicSale" => !empty($ido['crowdsale_alloc_supply']) ? (float)$ido['crowdsale_alloc_supply'] : null,
                    "privateSale" => null,
                    "ecosystem" => null,
                    "liquidity" => null,
                    "treasury" => null,
                    "marketing" => null,
                    "maxSupply" => $projectTotalSupply,
                    "allocations" => is_array($n8nAllocations) ? $n8nAllocations : [
                        ["name" => "Team & Advisors", "value" => null, "percentage" => null],
                        ["name" => "Public Sale", "value" => !empty($ido['crowdsale_tokenforsale']) ? (float)$ido['crowdsale_tokenforsale'] : null, "percentage" => !empty($ido['crowdsale_alloc_supply']) ? (float)$ido['crowdsale_alloc_supply'] : null],
                        ["name" => "Private Sale", "value" => null, "percentage" => null],
                        ["name" => "Ecosystem", "value" => null, "percentage" => null],
                        ["name" => "Liquidity", "value" => null, "percentage" => null],
                        ["name" => "Treasury", "value" => null, "percentage" => null],
                        ["name" => "Marketing", "value" => null, "percentage" => null]
                    ]
                ],
                // Funding information
                "funding" => [
                    "total" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                    "totalRaised" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                    "projectName" => $projectName,
                    "description" => null,
                    "publicSale" => !empty($ido['crowdsale_tokenforsale']) ? (float)$ido['crowdsale_tokenforsale'] : null,
                    "fundingRounds" => null,
                    "rounds" => [
                        [
                            "name" => "Public Sale",
                            "amount" => !empty($ido['crowdsale_raise']) ? (float)$ido['crowdsale_raise'] : null,
                            "date" => !empty($ido['crowdsale_startdate']) ? date('Y-m-d', $ido['crowdsale_startdate']) : null
                        ]
                    ],
                    "valuations" => [
                        "seed" => null,
                        "private" => null,
                        "public" => $projectMarketCap
                    ],
                    "saleStartDate" => !empty($ido['crowdsale_startdate']) ? $ido['crowdsale_startdate'] : null,
                    "saleEndDate" => !empty($ido['crowdsale_enddate']) ? $ido['crowdsale_enddate'] : null,
                    "athRoi" => !empty($ido['crowdsale_ath_roi']) ? (float)$ido['crowdsale_ath_roi'] : null,
                    "currentRoi" => !empty($ido['crowdsale_roi']) ? (float)$ido['crowdsale_roi'] : null
                ],
                // Tags
                "tags" => !empty($tags) ? $tags : null,
                // Team information (simplified)
                "team" => !empty($teamMembers) ? array_slice($teamMembers, 0, 3) : null, // Only first 3 members for basic version
                // Contract information (simplified)
                "contracts" => !empty($contracts) ? $contracts : null,
                // Launchpad information
                "launchpadInfo" => [
                    "name" => !empty($ido['lp_name']) ? $ido['lp_name'] : null,
                    "image" => !empty($ido['lp_image']) ? $ido['lp_image'] : null
                ]
            ];
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Creating success response (simple)");
            $response = new SuccessResult($formattedResponse);
            StripeLogger::log(StripeLogLevel::DEBUG, "IDO DETAIL - Sending success response (simple)");
            $response->send();
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "IDO DETAIL - IDO not found (simple)", ['id' => $id]);
            $response = new ErrorResult('IDO not found.');
            $response->send(404);
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::CRITICAL, "IDO DETAIL - Exception in get_ido_detail_by_id: " . $e->getMessage(), [
            'id' => $id,
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        $response = new ErrorResult('Internal server error occurred.');
        $response->send(500);
    }
}
