<?php



// return [
//     'stripe' => [
//         'secret_key' => 'sk_test_51QYDFqJC2HfyYehC2q2IFy7U8Tqioer0vM9ViLTOv5iQQIwWwE805ot0SCR9Ng1oXoCTxsJlCaDddfcF3flpPEDC006zmDj81i',
//         'public_key' => 'pk_test_51QYDFqJC2HfyYehCHO9N6olmmll2jXWlM78cpdLhcDOc46AN7dl3KdBLjG4mmqnPTKlIRMFTYgbihQyJClrFbx8o00oubBLwoV',
//         'webhook_secret' => 'whsec_LfzwQ7lyqSTr7FwRXByLzY190z5JA07p',
//         // Diğer gizli bilgiler (isteğe bağlı)
//         // Eğer Connect kullanıyorsanız
//         'account_id' => 'acct_1QYDFqJC2HfyYehC',
//     ],
//     'google' => [
//         'client_id' => '************-9m45dcrtfp5sslqisi22n39k520q1lv4.apps.googleusercontent.com',
//         'client_secret' => '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',
//     ],
// ];




$stripeSecretKey = 'sk_test_51QYDFqJC2HfyYehC2q2IFy7U8Tqioer0vM9ViLTOv5iQQIwWwE805ot0SCR9Ng1oXoCTxsJlCaDddfcF3flpPEDC006zmDj81i';
$stripePublicKey = 'pk_test_51QYDFqJC2HfyYehCHO9N6olmmll2jXWlM78cpdLhcDOc46AN7dl3KdBLjG4mmqnPTKlIRMFTYgbihQyJClrFbx8o00oubBLwoV';
$webhookSecret = 'whsec_LfzwQ7lyqSTr7FwRXByLzY190z5JA07p';

// Define constants for Stripe
define('STRIPE_WEBHOOK_SECRET', $webhookSecret);
// Diğer gizli bilgiler (isteğe bağlı)
// Eğer Connect kullanıyorsanız
$stripeAccountId = 'acct_1QYDFqJC2HfyYehC';
$google_client_id = '************-9m45dcrtfp5sslqisi22n39k520q1lv4.apps.googleusercontent.com';
$google_client_secret = '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';
